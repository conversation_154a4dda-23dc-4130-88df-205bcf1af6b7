<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HIK-Connect PC Interface</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏠</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #e74c3c;
        }

        .status-dot.connected {
            background: #27ae60;
        }

        .login-section {
            padding: 30px;
            border-bottom: 1px solid #ecf0f1;
        }

        .login-form {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-group label {
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input {
            padding: 10px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .devices-section {
            padding: 30px;
        }

        .devices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .device-card {
            border: 2px solid #ecf0f1;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s;
            background: #fafafa;
        }

        .device-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .device-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .device-status {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .device-status.online {
            background: #d5f4e6;
            color: #27ae60;
        }

        .device-status.offline {
            background: #fadbd8;
            color: #e74c3c;
        }

        .device-info {
            margin-bottom: 15px;
            font-size: 14px;
            color: #7f8c8d;
        }

        .device-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .control-btn.arm {
            background: #e74c3c;
            color: white;
        }

        .control-btn.disarm {
            background: #27ae60;
            color: white;
        }

        .control-btn.partial {
            background: #f39c12;
            color: white;
        }

        .control-btn.settings {
            background: #95a5a6;
            color: white;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .error {
            background: #fadbd8;
            color: #e74c3c;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .success {
            background: #d5f4e6;
            color: #27ae60;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .login-form {
                flex-direction: column;
                align-items: stretch;
            }
            
            .devices-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 HIK-Connect PC Interface</h1>
            <div class="status">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Disconnected</span>
            </div>
        </div>

        <div class="login-section">
            <div class="login-form">
                <div class="form-group">
                    <label for="username">Username/Email:</label>
                    <input type="text" id="username" placeholder="Enter your HIK-Connect username">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" placeholder="Enter your password">
                </div>
                <button class="btn btn-primary" id="loginBtn" onclick="login()">Connect</button>
                <button class="btn btn-danger hidden" id="logoutBtn" onclick="logout()">Disconnect</button>
            </div>
            <div id="loginMessage"></div>
        </div>

        <div class="devices-section">
            <h2>🔐 Your Devices (<span id="deviceCount">0</span>)</h2>
            <div id="devicesContainer">
                <div class="loading hidden" id="loadingDevices">
                    <p>🔄 Loading your devices...</p>
                </div>
                <div class="devices-grid" id="devicesGrid"></div>
            </div>
        </div>
    </div>

    <script>
        let isLoggedIn = false;
        let sessionId = null;
        let devices = [];

        function showMessage(message, type = 'error') {
            const messageDiv = document.getElementById('loginMessage');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }

        function updateStatus(connected) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            const loginBtn = document.getElementById('loginBtn');
            const logoutBtn = document.getElementById('logoutBtn');

            if (connected) {
                statusDot.classList.add('connected');
                statusText.textContent = 'Connected';
                loginBtn.classList.add('hidden');
                logoutBtn.classList.remove('hidden');
            } else {
                statusDot.classList.remove('connected');
                statusText.textContent = 'Disconnected';
                loginBtn.classList.remove('hidden');
                logoutBtn.classList.add('hidden');
            }
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showMessage('Please enter both username and password');
                return;
            }

            try {
                showMessage('🔄 Connecting to HIK-Connect...', 'success');
                
                // This would call your backend API
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    sessionId = result.sessionId;
                    isLoggedIn = true;
                    updateStatus(true);
                    showMessage('✅ Successfully connected!', 'success');
                    await loadDevices();
                } else {
                    showMessage(`❌ Login failed: ${result.error}`);
                }
            } catch (error) {
                showMessage(`❌ Connection error: ${error.message}`);
            }
        }

        async function logout() {
            isLoggedIn = false;
            sessionId = null;
            devices = [];
            updateStatus(false);
            document.getElementById('devicesGrid').innerHTML = '';
            document.getElementById('deviceCount').textContent = '0';
            showMessage('👋 Disconnected from HIK-Connect', 'success');
        }

        async function loadDevices() {
            if (!isLoggedIn) return;

            const loadingDiv = document.getElementById('loadingDevices');
            const devicesGrid = document.getElementById('devicesGrid');
            
            loadingDiv.classList.remove('hidden');
            devicesGrid.innerHTML = '';

            try {
                const response = await fetch('/api/devices', {
                    headers: {
                        'Authorization': `Bearer ${sessionId}`
                    }
                });

                const result = await response.json();

                if (result.success) {
                    devices = result.devices;
                    renderDevices();
                    document.getElementById('deviceCount').textContent = devices.length;
                } else {
                    showMessage(`❌ Failed to load devices: ${result.error}`);
                }
            } catch (error) {
                showMessage(`❌ Error loading devices: ${error.message}`);
            } finally {
                loadingDiv.classList.add('hidden');
            }
        }

        function renderDevices() {
            const devicesGrid = document.getElementById('devicesGrid');
            devicesGrid.innerHTML = '';

            devices.forEach(device => {
                const deviceCard = createDeviceCard(device);
                devicesGrid.appendChild(deviceCard);
            });
        }

        function createDeviceCard(device) {
            const card = document.createElement('div');
            card.className = 'device-card';
            
            const statusClass = device.status === 1 ? 'online' : 'offline';
            const statusText = device.status === 1 ? 'Online' : 'Offline';

            card.innerHTML = `
                <div class="device-header">
                    <div class="device-name">${device.name}</div>
                    <div class="device-status ${statusClass}">${statusText}</div>
                </div>
                <div class="device-info">
                    <div>📱 ${device.deviceType}</div>
                    <div>🔢 ${device.deviceSerial}</div>
                    <div>🔧 ${device.switchCount || 0} controls available</div>
                </div>
                <div class="device-controls">
                    <button class="control-btn arm" onclick="controlDevice('${device.deviceSerial}', 'arm')">🔴 Arm</button>
                    <button class="control-btn disarm" onclick="controlDevice('${device.deviceSerial}', 'disarm')">🟢 Disarm</button>
                    <button class="control-btn partial" onclick="controlDevice('${device.deviceSerial}', 'partial')">🟡 Partial</button>
                    <button class="control-btn settings" onclick="openSettings('${device.deviceSerial}')">⚙️ Settings</button>
                </div>
            `;

            return card;
        }

        async function controlDevice(deviceSerial, action) {
            if (!isLoggedIn) {
                showMessage('Please login first');
                return;
            }

            try {
                showMessage(`🔄 ${action.charAt(0).toUpperCase() + action.slice(1)}ing device...`, 'success');

                const response = await fetch('/api/control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${sessionId}`
                    },
                    body: JSON.stringify({ deviceSerial, action })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(`✅ Device ${action} successful!`, 'success');
                } else {
                    showMessage(`❌ Device control failed: ${result.error}`);
                }
            } catch (error) {
                showMessage(`❌ Control error: ${error.message}`);
            }
        }

        function openSettings(deviceSerial) {
            showMessage('⚙️ Device settings panel would open here', 'success');
            // This would open a modal or new page with device settings
        }

        // Initialize the interface
        updateStatus(false);
    </script>
</body>
</html>
