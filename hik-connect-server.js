#!/usr/bin/env node

const express = require('express');
const cors = require('cors');
const path = require('path');
const HikConnectAPI = require('./src/api');
const HikConnectClient = require('./src/client');

class HikConnectWebServer {
  constructor() {
    this.app = express();
    this.port = 1500;
    this.sessions = new Map(); // Store active sessions
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());
    this.app.use(express.static('.'));
    
    // Logging middleware
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  setupRoutes() {
    // Serve the main interface
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'hik-connect-web-interface.html'));
    });

    // Login endpoint
    this.app.post('/api/login', async (req, res) => {
      try {
        const { username, password } = req.body;
        
        if (!username || !password) {
          return res.json({ success: false, error: 'Username and password required' });
        }

        console.log(`🔐 Login attempt for: ${username}`);

        // Create HIK-Connect client
        const hikConnectAPI = new HikConnectAPI({ baseUrl: 'https://api.hik-connect.com' });
        const hikConnectClient = new HikConnectClient({ 
          hikConnectAPI, 
          ignoredLocks: [], 
          log: { 
            debug: (msg) => console.log(`[DEBUG] ${msg}`), 
            error: (msg) => console.error(`[ERROR] ${msg}`) 
          } 
        });

        // Attempt login
        await hikConnectClient.login({ account: username, password });
        
        // Generate session ID
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Store session
        this.sessions.set(sessionId, {
          client: hikConnectClient,
          username,
          loginTime: new Date(),
          lastActivity: new Date()
        });

        console.log(`✅ Login successful for: ${username}`);
        res.json({ success: true, sessionId });

      } catch (error) {
        console.error(`❌ Login failed:`, error.message);
        res.json({ success: false, error: error.message });
      }
    });

    // Get devices endpoint
    this.app.get('/api/devices', async (req, res) => {
      try {
        const sessionId = req.headers.authorization?.replace('Bearer ', '');
        
        if (!sessionId || !this.sessions.has(sessionId)) {
          return res.json({ success: false, error: 'Invalid session' });
        }

        const session = this.sessions.get(sessionId);
        session.lastActivity = new Date();

        console.log(`📱 Fetching devices for: ${session.username}`);

        // Get all devices using our updated method
        const allDevicesData = await session.client._getAllDevices();
        const devices = allDevicesData.data.deviceInfos;
        const switchStatusInfos = allDevicesData.data.switchStatusInfos;

        // Enhance device data with switch count
        const enhancedDevices = devices.map(device => ({
          ...device,
          switchCount: switchStatusInfos[device.deviceSerial]?.length || 0
        }));

        console.log(`✅ Found ${enhancedDevices.length} devices`);
        res.json({ success: true, devices: enhancedDevices });

      } catch (error) {
        console.error(`❌ Error fetching devices:`, error.message);
        res.json({ success: false, error: error.message });
      }
    });

    // Device control endpoint
    this.app.post('/api/control', async (req, res) => {
      try {
        const sessionId = req.headers.authorization?.replace('Bearer ', '');
        const { deviceSerial, action } = req.body;
        
        if (!sessionId || !this.sessions.has(sessionId)) {
          return res.json({ success: false, error: 'Invalid session' });
        }

        if (!deviceSerial || !action) {
          return res.json({ success: false, error: 'Device serial and action required' });
        }

        const session = this.sessions.get(sessionId);
        session.lastActivity = new Date();

        console.log(`🎮 Control request: ${action} for device ${deviceSerial}`);

        // Map actions to API calls
        const actionMap = {
          'arm': 'arm',
          'disarm': 'disarm', 
          'partial': 'partialArm'
        };

        const apiAction = actionMap[action];
        if (!apiAction) {
          return res.json({ success: false, error: 'Invalid action' });
        }

        // Attempt to control the device
        // Note: Based on our earlier testing, these endpoints return 404
        // But we'll try anyway and provide meaningful feedback
        try {
          const result = await this.attemptDeviceControl(session.client, deviceSerial, apiAction);
          res.json({ success: true, result });
        } catch (controlError) {
          // Expected based on our testing - provide helpful message
          if (controlError.message.includes('404') || controlError.message.includes('not found')) {
            res.json({ 
              success: false, 
              error: 'Device control not available through current API. This feature may require different permissions or API endpoints.' 
            });
          } else {
            res.json({ success: false, error: controlError.message });
          }
        }

      } catch (error) {
        console.error(`❌ Error controlling device:`, error.message);
        res.json({ success: false, error: error.message });
      }
    });

    // Device settings endpoint
    this.app.get('/api/device/:serial/settings', async (req, res) => {
      try {
        const sessionId = req.headers.authorization?.replace('Bearer ', '');
        const { serial } = req.params;
        
        if (!sessionId || !this.sessions.has(sessionId)) {
          return res.json({ success: false, error: 'Invalid session' });
        }

        const session = this.sessions.get(sessionId);
        session.lastActivity = new Date();

        console.log(`⚙️ Getting settings for device: ${serial}`);

        // Get device settings (this will likely fail based on our testing)
        try {
          const settings = await this.getDeviceSettings(session.client, serial);
          res.json({ success: true, settings });
        } catch (settingsError) {
          res.json({ 
            success: false, 
            error: 'Device settings not accessible through current API. This feature may require different permissions.' 
          });
        }

      } catch (error) {
        console.error(`❌ Error getting device settings:`, error.message);
        res.json({ success: false, error: error.message });
      }
    });

    // Session cleanup endpoint
    this.app.post('/api/logout', (req, res) => {
      const sessionId = req.headers.authorization?.replace('Bearer ', '');
      
      if (sessionId && this.sessions.has(sessionId)) {
        const session = this.sessions.get(sessionId);
        console.log(`👋 Logout: ${session.username}`);
        this.sessions.delete(sessionId);
      }

      res.json({ success: true });
    });

    // Health check
    this.app.get('/api/health', (req, res) => {
      res.json({ 
        status: 'ok', 
        activeSessions: this.sessions.size,
        uptime: process.uptime()
      });
    });
  }

  async attemptDeviceControl(client, deviceSerial, action) {
    // This is where we would implement device control
    // Based on our testing, these endpoints return 404, but we'll structure it properly
    const axios = require('axios');
    
    const baseUrl = client._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: client._sessionId
    };

    // Try the alarm control endpoint
    const url = `${baseUrl}/v3/devconfig/v1/call/${deviceSerial}/0/remote/${action}`;
    
    const response = await axios({
      method: 'post',
      url,
      headers,
      timeout: 10000
    });

    return response.data;
  }

  async getDeviceSettings(client, deviceSerial) {
    // This is where we would implement device settings retrieval
    // Based on our testing, these endpoints return 404, but we'll structure it properly
    const axios = require('axios');
    
    const baseUrl = client._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: client._sessionId
    };

    // Try the device config endpoint
    const url = `${baseUrl}/v3/devconfig/v1/devices/${deviceSerial}/config`;
    
    const response = await axios({
      method: 'get',
      url,
      headers,
      timeout: 10000
    });

    return response.data;
  }

  startSessionCleanup() {
    // Clean up inactive sessions every 30 minutes
    setInterval(() => {
      const now = new Date();
      const expiredSessions = [];

      for (const [sessionId, session] of this.sessions) {
        const inactiveTime = now - session.lastActivity;
        if (inactiveTime > 30 * 60 * 1000) { // 30 minutes
          expiredSessions.push(sessionId);
        }
      }

      expiredSessions.forEach(sessionId => {
        const session = this.sessions.get(sessionId);
        console.log(`🧹 Cleaning up expired session for: ${session.username}`);
        this.sessions.delete(sessionId);
      });

      if (expiredSessions.length > 0) {
        console.log(`🧹 Cleaned up ${expiredSessions.length} expired sessions`);
      }
    }, 30 * 60 * 1000);
  }

  start() {
    this.startSessionCleanup();
    
    this.app.listen(this.port, () => {
      console.log(`🌐 HIK-Connect Web Interface running at:`);
      console.log(`   Local:    http://localhost:${this.port}`);
      console.log(`   Network:  http://YOUR_IP:${this.port}`);
      console.log(`\n📱 Open the URL in your browser to access the interface`);
      console.log(`🔐 Use your HIK-Connect credentials to login`);
    });
  }
}

// Start the server
if (require.main === module) {
  const server = new HikConnectWebServer();
  server.start();
}

module.exports = HikConnectWebServer;
