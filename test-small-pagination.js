#!/usr/bin/env node

const HikConnectAPI = require('./src/api');
const HikConnectClient = require('./src/client');
const axios = require('axios');

class SmallPaginationTester {
  constructor(config) {
    this.config = config;
    const hikConnectAPI = new HikConnectAPI({ baseUrl: config.api_url || 'https://api.hik-connect.com' });
    this.hikConnectClient = new HikConnectClient({ 
      hikConnectAPI, 
      ignoredLocks: [], 
      log: { debug: console.log, error: console.error } 
    });
  }

  async login() {
    const { account, password } = this.config;
    if (!account || !password) {
      throw new Error('Account and password required in config');
    }
    await this.hikConnectClient.login({ account, password });
  }

  // Test with small page sizes to see actual pagination behavior
  async testSmallPagination() {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log('🔍 Testing small pagination to find all devices...\n');

    const allUniqueDevices = new Map(); // Use Map to track unique devices by serial
    
    // Test with small page sizes and different offsets
    for (let offset = 0; offset < 50; offset += 8) {
      try {
        const url = `${baseUrl}/v3/userdevices/v1/devices/pagelist?groupId=-1&limit=8&offset=${offset}&filter=TIME_PLAN,CONNECTION,SWITCH,STATUS,STATUS_EXT,WIFI,NODISTURB,P2P,KMS,HIDDNS`;
        
        console.log(`📄 Testing offset ${offset}...`);
        
        const response = await axios({
          method: 'get',
          url,
          headers,
          timeout: 10000
        });
        
        const pageDevices = response.data.deviceInfos || [];
        
        if (pageDevices.length === 0) {
          console.log(`   No devices found at offset ${offset}`);
          break;
        }
        
        console.log(`   Found ${pageDevices.length} devices:`);
        
        pageDevices.forEach((device, index) => {
          const deviceKey = device.deviceSerial;
          if (!allUniqueDevices.has(deviceKey)) {
            allUniqueDevices.set(deviceKey, device);
            console.log(`   ${index + 1}. NEW: ${device.name} (${device.deviceSerial})`);
          } else {
            console.log(`   ${index + 1}. DUP: ${device.name} (${device.deviceSerial})`);
          }
        });
        
        console.log('');
        
      } catch (error) {
        console.error(`❌ Error at offset ${offset}:`, error.message);
        break;
      }
    }

    console.log(`\n🎉 Total unique devices found: ${allUniqueDevices.size}\n`);
    
    console.log('📋 All Unique Devices:');
    console.log('═══════════════════════\n');
    
    let index = 1;
    for (const [serial, device] of allUniqueDevices) {
      console.log(`${index}. ${device.name}`);
      console.log(`   Serial: ${device.deviceSerial}`);
      console.log(`   Type: ${device.deviceType}`);
      console.log(`   Category: ${device.deviceCategory}`);
      console.log('');
      index++;
    }

    return Array.from(allUniqueDevices.values());
  }

  // Test different API approaches that might reveal more devices
  async testAlternativeApproaches() {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log('🔍 Testing alternative approaches to find more devices...\n');

    // Test different client types (maybe mobile app uses different client type)
    const clientTypes = ['IECMOBILE', 'IECHIKCONNECT', 'IECWEB', 'MOBILE', 'WEB'];
    
    for (const clientType of clientTypes) {
      try {
        const testHeaders = { ...headers, clientType };
        const url = `${baseUrl}/v3/userdevices/v1/devices/pagelist?groupId=-1&limit=200&offset=0&filter=TIME_PLAN,CONNECTION,SWITCH,STATUS,STATUS_EXT,WIFI,NODISTURB,P2P,KMS,HIDDNS`;
        
        console.log(`📱 Testing client type: ${clientType}`);
        
        const response = await axios({
          method: 'get',
          url,
          headers: testHeaders,
          timeout: 10000
        });
        
        const deviceCount = response.data.deviceInfos ? response.data.deviceInfos.length : 0;
        console.log(`   Found ${deviceCount} devices`);
        
        if (deviceCount > 8) {
          console.log('   🎉 Found more devices with this client type!');
          response.data.deviceInfos.forEach((device, index) => {
            console.log(`   ${index + 1}. ${device.name} (${device.deviceSerial})`);
          });
        }
        
      } catch (error) {
        console.log(`   ❌ Failed with ${clientType}: ${error.response?.status || error.message}`);
      }
    }

    console.log('\n🔍 Testing different feature codes...\n');

    // Test different feature codes
    const featureCodes = ['hik-connect', 'hikconnect', 'hikvision', 'alarm', 'security'];
    
    for (const featureCode of featureCodes) {
      try {
        const testHeaders = { ...headers, featureCode };
        const url = `${baseUrl}/v3/userdevices/v1/devices/pagelist?groupId=-1&limit=200&offset=0&filter=TIME_PLAN,CONNECTION,SWITCH,STATUS,STATUS_EXT,WIFI,NODISTURB,P2P,KMS,HIDDNS`;
        
        console.log(`🔧 Testing feature code: ${featureCode}`);
        
        const response = await axios({
          method: 'get',
          url,
          headers: testHeaders,
          timeout: 10000
        });
        
        const deviceCount = response.data.deviceInfos ? response.data.deviceInfos.length : 0;
        console.log(`   Found ${deviceCount} devices`);
        
        if (deviceCount > 8) {
          console.log('   🎉 Found more devices with this feature code!');
        }
        
      } catch (error) {
        console.log(`   ❌ Failed with ${featureCode}: ${error.response?.status || error.message}`);
      }
    }
  }
}

// Main execution
async function main() {
  const config = {
    account: '<EMAIL>',
    password: 'Hatchet1',
    api_url: 'https://api.hik-connect.com'
  };

  const tester = new SmallPaginationTester(config);
  
  try {
    console.log('🔐 Logging into HikConnect...');
    await tester.login();
    console.log('✅ Login successful!\n');

    await tester.testSmallPagination();
    await tester.testAlternativeApproaches();

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

if (require.main === module) {
  main();
}

module.exports = SmallPaginationTester;
