const HikConnectAPI = require('./src/api');
const HikConnectClient = require('./src/client');
const axios = require('axios');

class DeviceInspector {
  constructor(config) {
    this.config = config;
    const hikConnectAPI = new HikConnectAPI({ baseUrl: config.api_url || 'https://api.hik-connect.com' });
    this.hikConnectClient = new HikConnectClient({ 
      hikConnectAPI, 
      ignoredLocks: [], 
      log: { debug: console.log, error: console.error } 
    });
  }

  async login() {
    const { account, password } = this.config;
    if (!account || !password) {
      throw new Error('Account and password required in config');
    }
    await this.hikConnectClient.login({ account, password });
  }

  async getAllDeviceInfo() {
    try {
      const response = await axios({
        method: 'get',
        url: this.hikConnectClient._hikConnectAPI.getDevicesUrl(),
        headers: Object.assign({}, 
          { clientType: 'IECMOBILE', lang: 'en-US', featureCode: 'hik-connect' }, 
          { sessionId: this.hikConnectClient._sessionId }
        )
      });

      return response.data;
    } catch (error) {
      console.error('Failed to get device info:', error.message);
      return null;
    }
  }

  async listAllDevices() {
    console.log('🔍 Fetching all devices...\n');
    
    const deviceData = await this.getAllDeviceInfo();
    if (!deviceData) return;

    const { deviceInfos, statusInfos } = deviceData;

    console.log(`📱 Found ${deviceInfos.length} devices:\n`);

    deviceInfos.forEach((device, index) => {
      const status = statusInfos[device.deviceSerial];
      
      console.log(`━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`);
      console.log(`🏠 Device ${index + 1}: ${device.name}`);
      console.log(`━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`);
      
      // Basic Device Info
      console.log(`📋 Basic Information:`);
      console.log(`   Serial Number: ${device.deviceSerial}`);
      console.log(`   Full Serial: ${device.fullSerial}`);
      console.log(`   Type: ${device.deviceType}`);
      console.log(`   Version: ${device.version}`);
      console.log(`   Category: ${device.deviceCategory}`);
      
      // Connection Status
      if (status) {
        console.log(`\n🔗 Connection Status:`);
        console.log(`   Online: ${status.optionals.isOnline ? '✅ Yes' : '❌ No'}`);
        console.log(`   Last Online: ${status.optionals.lastOnlineTime || 'N/A'}`);
        
        // Lock Information
        if (status.optionals.lockNum) {
          console.log(`\n🔐 Lock Information:`);
          const lockInfo = JSON.parse(status.optionals.lockNum);
          Object.entries(lockInfo).forEach(([channel, lockCount]) => {
            console.log(`   Channel ${channel}: ${lockCount} lock(s)`);
          });
        }

        // Additional Settings
        console.log(`\n⚙️  Device Settings:`);
        if (status.optionals.wifiInfo) {
          const wifi = JSON.parse(status.optionals.wifiInfo);
          console.log(`   WiFi SSID: ${wifi.ssid || 'N/A'}`);
          console.log(`   WiFi Signal: ${wifi.signalLevel || 'N/A'}`);
        }
        
        if (status.optionals.p2pInfo) {
          console.log(`   P2P Status: ${status.optionals.p2pInfo}`);
        }

        // Show all available optionals
        console.log(`\n📊 Available Data Fields:`);
        Object.keys(status.optionals).forEach(key => {
          console.log(`   - ${key}`);
        });
      }
      
      console.log(`\n`);
    });
  }

  async listOnlyLocks() {
    console.log('🔐 Fetching lock devices only...\n');
    
    const locks = await this.hikConnectClient.getLocks();
    
    console.log(`🔒 Found ${locks.length} lock(s):\n`);
    
    locks.forEach((lock, index) => {
      console.log(`━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`);
      console.log(`🔐 Lock ${index + 1}: ${lock.name}`);
      console.log(`━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`);
      console.log(`   ID: ${lock.id}`);
      console.log(`   Serial: ${lock.serial}`);
      console.log(`   Type: ${lock.type}`);
      console.log(`   Version: ${lock.version}`);
      console.log(`   Channel: ${lock.lockChannel}`);
      console.log(`   Index: ${lock.lockIndex}`);
      console.log(``);
    });
  }

  async testUnlock(deviceSerial, lockChannel, lockIndex) {
    console.log(`🔓 Testing unlock for device ${deviceSerial}, channel ${lockChannel}, index ${lockIndex}...`);
    
    try {
      await this.hikConnectClient.unlock(deviceSerial, lockChannel, lockIndex);
      console.log('✅ Unlock command sent successfully!');
    } catch (error) {
      console.error('❌ Unlock failed:', error.message);
    }
  }

  async exportDeviceData() {
    const deviceData = await this.getAllDeviceInfo();
    if (!deviceData) return;

    const fs = require('fs');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `device-data-${timestamp}.json`;
    
    fs.writeFileSync(filename, JSON.stringify(deviceData, null, 2));
    console.log(`📁 Device data exported to: ${filename}`);
  }
}

// Usage example
async function main() {
  // You need to provide your HikConnect credentials
  const config = {
    account: 'YOUR_ACCOUNT',          // Replace with your account
    password: 'YOUR_PASSWORD',        // Replace with your password  
    api_url: 'https://api.hik-connect.com'  // Optional: use different API URL
  };

  const inspector = new DeviceInspector(config);
  
  try {
    console.log('🔐 Logging into HikConnect...');
    await inspector.login();
    console.log('✅ Login successful!\n');

    // List all devices with full details
    await inspector.listAllDevices();
    
    // List only locks
    await inspector.listOnlyLocks();
    
    // Export raw data for analysis
    await inspector.exportDeviceData();
    
    // Test unlock (uncomment and modify as needed)
    // await inspector.testUnlock('YOUR_DEVICE_SERIAL', 0, 0);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Uncomment to run directly
// main();

module.exports = DeviceInspector;
