#!/usr/bin/env node

const DeviceInspector = require('./device-inspector');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise(resolve => {
    rl.question(question, resolve);
  });
}

async function main() {
  console.log('🏠 HikConnect Device Inspector');
  console.log('═══════════════════════════════\n');

  // Get credentials
  const account = await askQuestion('Enter your HikConnect account: ');
  const password = await askQuestion('Enter your password: ');
  const apiUrl = await askQuestion('API URL (press Enter for default): ') || 'https://api.hik-connect.com';

  const config = { account, password, api_url: apiUrl };
  const inspector = new DeviceInspector(config);

  try {
    console.log('\n🔐 Logging into HikConnect...');
    await inspector.login();
    console.log('✅ Login successful!\n');

    while (true) {
      console.log('\n📋 What would you like to do?');
      console.log('1. List all devices (detailed)');
      console.log('2. List only locks');
      console.log('3. Export device data to JSON');
      console.log('4. Test unlock a specific lock');
      console.log('5. Exit');

      const choice = await askQuestion('\nEnter your choice (1-5): ');

      switch (choice) {
        case '1':
          await inspector.listAllDevices();
          break;
        case '2':
          await inspector.listOnlyLocks();
          break;
        case '3':
          await inspector.exportDeviceData();
          break;
        case '4':
          const serial = await askQuestion('Enter device serial: ');
          const channel = parseInt(await askQuestion('Enter lock channel: '));
          const index = parseInt(await askQuestion('Enter lock index: '));
          await inspector.testUnlock(serial, channel, index);
          break;
        case '5':
          console.log('👋 Goodbye!');
          process.exit(0);
        default:
          console.log('❌ Invalid choice. Please try again.');
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    rl.close();
  }
}

main();
