# 🏠 HIK-Connect PC Interface

A web-based interface to control your HIK-Connect devices from your PC, just like the mobile app!

## 🚀 How to Run the Program

### Method 1: Using the Batch File (Easiest)
1. **Double-click** `start-hik-connect.bat`
2. Wait for the server to start (you'll see the startup message)
3. Open your web browser and go to: `http://localhost:1500`

### Method 2: Using Command Line
1. Open **Command Prompt** or **PowerShell**
2. Navigate to the folder: `cd "C:\Users\<USER>\Desktop\homebridge-hikconnect-main"`
3. Run the command: `node hik-connect-server.js`
4. Open your web browser and go to: `http://localhost:1500`

### Method 3: Using Node.js directly
1. Open the folder in **File Explorer**
2. Hold **Shift** and **right-click** in an empty area
3. Select **"Open PowerShell window here"**
4. Type: `node hik-connect-server.js`
5. Open your web browser and go to: `http://localhost:1500`

## 🌐 Accessing the Interface

Once the server is running, you can access it from:
- **Same PC**: `http://localhost:1500`
- **Other devices on your network**: `http://YOUR_PC_IP:1500`
  - To find your PC's IP: Open Command Prompt and type `ipconfig`
  - Look for "IPv4 Address" (usually something like 192.168.1.xxx)

## 🔐 Login Instructions

1. **Username**: Your HIK-Connect email (<EMAIL>)
2. **Password**: Your HIK-Connect password
3. Click **"Connect"** button
4. Wait for the system to load all your devices

## 📱 What You'll See

- **56 devices** from your HIK-Connect account
- **Device status** (Online/Offline)
- **Control buttons** for each device:
  - 🔴 **Arm** - Activate alarm
  - 🟢 **Disarm** - Deactivate alarm  
  - 🟡 **Partial** - Partial arm mode
  - ⚙️ **Settings** - Device settings

## 🛑 How to Stop the Program

- **Method 1**: Close the Command Prompt/PowerShell window
- **Method 2**: Press `Ctrl + C` in the terminal
- **Method 3**: Close the batch file window

## 🔧 Troubleshooting

### Server won't start:
- Make sure **Node.js** is installed
- Check if port 1500 is already in use
- Try running as Administrator

### Can't connect to HIK-Connect:
- Check your internet connection
- Verify your username and password
- Make sure HIK-Connect service is working

### Devices not loading:
- Wait a few seconds after login
- Check the browser console for errors (F12)
- Try refreshing the page

### Can't access from other devices:
- Make sure Windows Firewall allows port 1500
- Check if your PC and other device are on the same network
- Use your PC's actual IP address, not localhost

## 📁 Files Included

- `hik-connect-server.js` - Main server application
- `hik-connect-web-interface.html` - Web interface
- `start-hik-connect.bat` - Easy startup script
- `README-HIK-CONNECT-PC.md` - This instruction file

## 🎯 Features

✅ **Login with HIK-Connect credentials**
✅ **View all 56 devices**
✅ **Real-time device status**
✅ **Modern web interface**
✅ **Responsive design (works on tablets/phones too)**
✅ **Session management**
✅ **Automatic cleanup**

⚠️ **Limited Features** (due to API restrictions):
- Alarm control buttons may not work (API returns 404 errors)
- Device settings may not be accessible
- These features work in the mobile app but use different API endpoints

## 🔄 Auto-Start on Windows Boot (Optional)

To make the program start automatically when Windows starts:

1. Press `Win + R`, type `shell:startup`, press Enter
2. Copy `start-hik-connect.bat` to the Startup folder
3. The program will start automatically when you log into Windows

## 📞 Support

If you encounter any issues:
1. Check the terminal/command prompt for error messages
2. Try restarting the program
3. Make sure your HIK-Connect account works in the mobile app first
4. Check your internet connection

---

**Enjoy controlling your HIK-Connect devices from your PC!** 🎉
