#!/usr/bin/env node

const HikConnectAPI = require('./src/api');
const HikConnectClient = require('./src/client');

class UpdatedPluginTester {
  constructor(config) {
    this.config = config;
    const hikConnectAPI = new HikConnectAPI({ baseUrl: config.api_url || 'https://api.hik-connect.com' });
    this.hikConnectClient = new HikConnectClient({ 
      hikConnectAPI, 
      ignoredLocks: [], 
      log: { debug: console.log, error: console.error } 
    });
  }

  async login() {
    const { account, password } = this.config;
    if (!account || !password) {
      throw new Error('Account and password required in config');
    }
    await this.hikConnectClient.login({ account, password });
  }

  async testUpdatedDeviceFetch() {
    console.log('🔍 Testing updated device fetching (with proper pagination)...\n');

    try {
      // Test the updated _getAllDevices method
      const allDevicesData = await this.hikConnectClient._getAllDevices();
      const devices = allDevicesData.data.deviceInfos;
      
      console.log(`🎉 Total devices found: ${devices.length}\n`);

      // Group devices by type and category
      const devicesByType = {};
      const devicesByCategory = {};
      
      devices.forEach(device => {
        // Group by type
        if (!devicesByType[device.deviceType]) {
          devicesByType[device.deviceType] = [];
        }
        devicesByType[device.deviceType].push(device);
        
        // Group by category
        if (!devicesByCategory[device.deviceCategory]) {
          devicesByCategory[device.deviceCategory] = [];
        }
        devicesByCategory[device.deviceCategory].push(device);
      });

      console.log('📊 Device Summary:');
      console.log('═══════════════════\n');
      
      console.log('📱 By Device Type:');
      Object.entries(devicesByType).forEach(([type, devices]) => {
        console.log(`   ${type}: ${devices.length} devices`);
      });
      
      console.log('\n🏷️  By Category:');
      Object.entries(devicesByCategory).forEach(([category, devices]) => {
        console.log(`   ${category}: ${devices.length} devices`);
      });

      return allDevicesData;
    } catch (error) {
      console.error('❌ Error testing updated device fetch:', error.message);
      return null;
    }
  }

  async testLockDetection() {
    console.log('\n🔐 Testing lock detection with all devices...\n');

    try {
      const locks = await this.hikConnectClient.getLocks();
      
      console.log(`🔓 Locks detected: ${locks.length}\n`);
      
      if (locks.length > 0) {
        console.log('📋 Detected Locks:');
        console.log('═══════════════════\n');
        
        locks.forEach((lock, index) => {
          console.log(`${index + 1}. ${lock.name}`);
          console.log(`   Serial: ${lock.serial}`);
          console.log(`   Type: ${lock.type}`);
          console.log(`   Lock Channel: ${lock.lockChannel}`);
          console.log(`   Lock Index: ${lock.lockIndex}`);
          console.log('');
        });
      } else {
        console.log('❌ No locks detected. This is likely due to the lockNum field issue we identified earlier.');
        console.log('   The devices are found, but the lock detection logic needs to be fixed.');
      }

      return locks;
    } catch (error) {
      console.error('❌ Error testing lock detection:', error.message);
      return [];
    }
  }

  async testDeviceCapabilities() {
    console.log('\n🔧 Testing device capabilities and switch controls...\n');

    try {
      const allDevicesData = await this.hikConnectClient._getAllDevices();
      const devices = allDevicesData.data.deviceInfos;
      const switchStatusInfos = allDevicesData.data.switchStatusInfos;

      console.log('📊 Device Capabilities Summary:');
      console.log('═══════════════════════════════\n');

      let totalSwitches = 0;
      let devicesWithSwitches = 0;

      devices.forEach((device, index) => {
        const switchInfo = switchStatusInfos[device.deviceSerial];
        const switchCount = switchInfo ? switchInfo.length : 0;
        
        if (switchCount > 0) {
          devicesWithSwitches++;
          totalSwitches += switchCount;
        }

        if (index < 5) { // Show details for first 5 devices
          console.log(`${index + 1}. ${device.name}`);
          console.log(`   Serial: ${device.deviceSerial}`);
          console.log(`   Type: ${device.deviceType}`);
          console.log(`   Category: ${device.deviceCategory}`);
          console.log(`   Status: ${device.status}`);
          console.log(`   Switches: ${switchCount} available`);
          
          if (switchCount > 0 && index === 0) {
            console.log('   Switch Types:');
            switchInfo.forEach((sw, swIndex) => {
              console.log(`     Switch ${swIndex}: Type ${sw.type}, Channel ${sw.channelNo}, Enabled: ${sw.enable}`);
            });
          }
          console.log('');
        }
      });

      console.log(`📈 Summary:`);
      console.log(`   Total devices: ${devices.length}`);
      console.log(`   Devices with switches: ${devicesWithSwitches}`);
      console.log(`   Total switches: ${totalSwitches}`);
      console.log(`   Average switches per device: ${(totalSwitches / devicesWithSwitches).toFixed(1)}`);

    } catch (error) {
      console.error('❌ Error testing device capabilities:', error.message);
    }
  }
}

// Main execution
async function main() {
  const config = {
    account: '<EMAIL>',
    password: 'Hatchet1',
    api_url: 'https://api.hik-connect.com'
  };

  const tester = new UpdatedPluginTester(config);
  
  try {
    console.log('🔐 Logging into HikConnect...');
    await tester.login();
    console.log('✅ Login successful!\n');

    await tester.testUpdatedDeviceFetch();
    await tester.testLockDetection();
    await tester.testDeviceCapabilities();

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

if (require.main === module) {
  main();
}

module.exports = UpdatedPluginTester;
