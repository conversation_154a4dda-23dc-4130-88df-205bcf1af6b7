const axios = require('axios');
const md5 = require('md5');
const qs = require('qs');
const jwtDecode = require('jwt-decode');

const {
  CLIENT_TYPE,
  FEATURE_CODE,
  LANGUAGE
} = require('./constants');

const DEFAULT_HEADERS = {
  clientType: CLIENT_TYPE,
  lang: LANGUAGE,
  featureCode: FEATURE_CODE
};

class HikConnectClient {
  constructor({ hikConnectAPI, ignoredLocks = [], log }) {
    this._log = log;
    this._sessionId = '';
    this._refreshSessionId = '';
    this._loginValidUntil = '';
    this._hikConnectAPI = hikConnectAPI;
    this._ignoredLocks = ignoredLocks;
  }

  async login({ account, password }) {
    try {
      const response = await axios({
        method: 'post',
        url: this._hikConnectAPI.getLoginUrl(),
        data: this._prepareLoginPayloadData({ account, password }),
        headers: DEFAULT_HEADERS
      });

      this._saveSessionData({
        sessionId: response.data.loginSession.sessionId,
        refreshSessionId: response.data.loginSession.rfSessionId
      });
    } catch (error) {
      throw new Error('Login failed, wrong account or password', error);
    }
  }

  async getLocks() {
    try {
      const allDevicesData = await this._getAllDevices();
      this._log.debug(allDevicesData);
      return this._transformDevicesToLocks(allDevicesData);
    } catch (error) {
      throw new Error('Failed to get locks', error);
    }
  }

  async _getAllDevices() {
    const allDevices = [];
    let allStatusInfos = {};
    let allSwitchStatusInfos = {};
    let offset = 0;
    const limit = 8; // API seems to have max page size of 8
    let hasMoreDevices = true;

    while (hasMoreDevices) {
      try {
        // Build URL with current offset
        const baseUrl = this._hikConnectAPI.getDevicesUrl();
        const url = baseUrl.replace('offset=0', `offset=${offset}`);

        const response = await axios({
          method: 'get',
          url: url,
          headers: Object.assign({}, DEFAULT_HEADERS, { sessionId: this._sessionId })
        });

        const pageDevices = response.data.deviceInfos || [];
        const pageStatusInfos = response.data.statusInfos || {};
        const pageSwitchStatusInfos = response.data.switchStatusInfos || {};

        if (pageDevices.length === 0) {
          hasMoreDevices = false;
        } else {
          // Add devices to our collection
          allDevices.push(...pageDevices);

          // Merge status info
          Object.assign(allStatusInfos, pageStatusInfos);
          Object.assign(allSwitchStatusInfos, pageSwitchStatusInfos);

          // Move to next page
          offset += limit;

          // Safety check to prevent infinite loops
          if (offset > 500) {
            this._log.debug('Safety limit reached (500 offset). Stopping device fetch.');
            hasMoreDevices = false;
          }
        }
      } catch (error) {
        this._log.debug(`Error fetching devices at offset ${offset}:`, error.message);
        hasMoreDevices = false;
      }
    }

    this._log.debug(`Total devices fetched: ${allDevices.length}`);

    return {
      data: {
        deviceInfos: allDevices,
        statusInfos: allStatusInfos,
        switchStatusInfos: allSwitchStatusInfos
      }
    };
  }

  async refreshSessionIfNeeded() {
    if (!this._isNewSessionNeeded(this._loginValidUntil)) {
      return false;
    }

    try {
      const response = await axios({
        method: 'put',
        url: this._hikConnectAPI.getRefreshSessionUrl(),
        data: this._prepareRefreshSessionPayloadData(this._refreshSessionId)
      });

      this._saveSessionData({
        sessionId: response.data.sessionInfo.sessionId,
        refreshSessionId: response.data.sessionInfo.refreshSessionId
      });
    } catch (error) {
      throw new Error('Could not refresh session', error);
    }
  }

  async unlock(deviceSerial, lockChannel, lockIndex) {
    try {
      await axios({
        method: 'put',
        url: this._hikConnectAPI.getUnlockUrl({ deviceSerial, lockChannel, lockIndex }),
        headers: Object.assign({}, DEFAULT_HEADERS, { sessionId: this._sessionId })
      });
    } catch (error) {
      throw new Error(`Unlock failed: ${deviceSerial}/${lockChannel}/${lockIndex}`, error);
    }
  }

  _isNewSessionNeeded(loginValidUntil) {
    const now = new Date();
    const validUntil = new Date(loginValidUntil * 1000);
    const differenceInHours = Math.abs(validUntil - now) / 36e5;
    return differenceInHours < 1;
  }

  _prepareLoginPayloadData({ account, password }) {
    return qs.stringify({ account, password: md5(password) });
  }

  _prepareRefreshSessionPayloadData(refreshSessionId) {
    return qs.stringify({ refreshSessionId, featureCode: FEATURE_CODE });
  }

  _saveSessionData({ sessionId, refreshSessionId }) {
    this._sessionId = sessionId;
    this._refreshSessionId = refreshSessionId;
    this._loginValidUntil = jwtDecode(sessionId).exp;
  }

  _transformDevicesToLocks(response) {
    return response.data.deviceInfos
      .filter(device => response.data.statusInfos[device.deviceSerial].optionals.lockNum)
      .reduce((locks, device) => {
        const deviceLocks = JSON.parse(response.data.statusInfos[device.deviceSerial].optionals.lockNum);
        for (const [lockChannel, numberOfLocks] of Object.entries(deviceLocks)) {
          [...Array(numberOfLocks)].forEach((_, lockIndex) => {
            const lockName = `${device.name}/${lockChannel}/${lockIndex}`;
            if (!this._ignoredLocks.includes(lockName)) {
              locks.push({
                id: device.fullSerial,
                name: lockName,
                serial: device.deviceSerial,
                type: device.deviceType,
                version: device.version,
                lockChannel: parseInt(lockChannel),
                lockIndex
              });
            }
          });
        }
        return locks;
      }, []);
  }
}

module.exports = HikConnectClient;
