{"name": "homebridge-hikconnect", "displayName": "HikConnect", "version": "1.3.2", "description": "A Homebridge plugin to communicate with Hikvision smart doorbells via Hik-Connect cloud and allows lock to be unlocked. It exposes doorbell locks as a lock accessories to Homekit.", "engines": {"node": ">=14.15.5", "homebridge": ">=1.3.0"}, "main": "src/index.js", "scripts": {"start": "nodemon", "test": "jest --collectCoverage", "lint": "eslint src/**/*", "test-watch": "jest --watch --collectCoverage", "inspect-devices": "node inspect-devices.js"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "ISC", "funding": {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/judge"}, "keywords": ["homebridge-plugin"], "devDependencies": {"eslint": "8.6.0", "homebridge": "1.3.9", "jest": "27.4.7", "jwt-encode": "1.0.1", "nodemon": "2.0.15"}, "dependencies": {"axios": "0.24.0", "cors": "^2.8.5", "express": "^5.1.0", "jwt-decode": "3.1.2", "md5": "2.3.0", "qs": "6.10.3"}}