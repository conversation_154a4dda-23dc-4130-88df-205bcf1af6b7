#!/usr/bin/env node

const HikConnectAPI = require('./src/api');
const HikConnectClient = require('./src/client');
const axios = require('axios');

class AllDevicesFetcher {
  constructor(config) {
    this.config = config;
    const hikConnectAPI = new HikConnectAPI({ baseUrl: config.api_url || 'https://api.hik-connect.com' });
    this.hikConnectClient = new HikConnectClient({ 
      hikConnectAPI, 
      ignoredLocks: [], 
      log: { debug: console.log, error: console.error } 
    });
  }

  async login() {
    const { account, password } = this.config;
    if (!account || !password) {
      throw new Error('Account and password required in config');
    }
    await this.hikConnectClient.login({ account, password });
  }

  // Get all devices by iterating through all pages
  async getAllDevices() {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log('🔍 Fetching ALL devices from all pages...\n');

    let allDevices = [];
    let allStatusInfos = {};
    let allSwitchStatusInfos = {};
    let offset = 0;
    const limit = 100;
    let hasMoreDevices = true;
    let pageNumber = 1;

    while (hasMoreDevices) {
      try {
        const url = `${baseUrl}/v3/userdevices/v1/devices/pagelist?groupId=-1&limit=${limit}&offset=${offset}&filter=TIME_PLAN,CONNECTION,SWITCH,STATUS,STATUS_EXT,WIFI,NODISTURB,P2P,KMS,HIDDNS`;
        
        console.log(`📄 Fetching page ${pageNumber} (offset: ${offset})...`);
        
        const response = await axios({
          method: 'get',
          url,
          headers,
          timeout: 10000
        });
        
        const pageDevices = response.data.deviceInfos || [];
        const pageStatusInfos = response.data.statusInfos || {};
        const pageSwitchStatusInfos = response.data.switchStatusInfos || {};
        
        console.log(`   Found ${pageDevices.length} devices on page ${pageNumber}`);
        
        if (pageDevices.length === 0) {
          hasMoreDevices = false;
          console.log('   No more devices found.');
        } else {
          // Add devices to our collection
          allDevices = allDevices.concat(pageDevices);
          
          // Merge status info
          Object.assign(allStatusInfos, pageStatusInfos);
          Object.assign(allSwitchStatusInfos, pageSwitchStatusInfos);
          
          // Show first and last device on this page
          console.log(`   First: ${pageDevices[0].name} (${pageDevices[0].deviceSerial})`);
          if (pageDevices.length > 1) {
            console.log(`   Last: ${pageDevices[pageDevices.length-1].name} (${pageDevices[pageDevices.length-1].deviceSerial})`);
          }
          
          // Move to next page
          offset += limit;
          pageNumber++;
          
          // Safety check to prevent infinite loops
          if (pageNumber > 20) {
            console.log('⚠️  Safety limit reached (20 pages). Stopping.');
            hasMoreDevices = false;
          }
        }
        
      } catch (error) {
        console.error(`❌ Error fetching page ${pageNumber}:`, error.message);
        hasMoreDevices = false;
      }
      
      console.log('');
    }

    console.log(`🎉 Total devices found: ${allDevices.length}\n`);

    // Group devices by type and category
    const devicesByType = {};
    const devicesByCategory = {};
    
    allDevices.forEach(device => {
      // Group by type
      if (!devicesByType[device.deviceType]) {
        devicesByType[device.deviceType] = [];
      }
      devicesByType[device.deviceType].push(device);
      
      // Group by category
      if (!devicesByCategory[device.deviceCategory]) {
        devicesByCategory[device.deviceCategory] = [];
      }
      devicesByCategory[device.deviceCategory].push(device);
    });

    console.log('📊 Device Summary:');
    console.log('═══════════════════\n');
    
    console.log('📱 By Device Type:');
    Object.entries(devicesByType).forEach(([type, devices]) => {
      console.log(`   ${type}: ${devices.length} devices`);
    });
    
    console.log('\n🏷️  By Category:');
    Object.entries(devicesByCategory).forEach(([category, devices]) => {
      console.log(`   ${category}: ${devices.length} devices`);
    });

    console.log('\n📋 All Devices:');
    console.log('═══════════════\n');
    
    allDevices.forEach((device, index) => {
      console.log(`${index + 1}. ${device.name}`);
      console.log(`   Serial: ${device.deviceSerial}`);
      console.log(`   Type: ${device.deviceType}`);
      console.log(`   Category: ${device.deviceCategory}`);
      console.log(`   Status: ${device.status}`);
      
      // Show switch info if available
      const switchInfo = allSwitchStatusInfos[device.deviceSerial];
      if (switchInfo && switchInfo.length > 0) {
        console.log(`   Switches: ${switchInfo.length} available`);
      }
      
      console.log('');
    });

    return {
      deviceInfos: allDevices,
      statusInfos: allStatusInfos,
      switchStatusInfos: allSwitchStatusInfos
    };
  }

  // Export all device data to a JSON file for analysis
  async exportAllDeviceData() {
    const allData = await this.getAllDevices();
    
    const fs = require('fs');
    const filename = 'all-devices-data.json';
    
    try {
      fs.writeFileSync(filename, JSON.stringify(allData, null, 2));
      console.log(`💾 All device data exported to: ${filename}`);
    } catch (error) {
      console.error('❌ Failed to export data:', error.message);
    }
    
    return allData;
  }
}

// Main execution
async function main() {
  const config = {
    account: '<EMAIL>',
    password: 'Hatchet1',
    api_url: 'https://api.hik-connect.com'
  };

  const fetcher = new AllDevicesFetcher(config);
  
  try {
    console.log('🔐 Logging into HikConnect...');
    await fetcher.login();
    console.log('✅ Login successful!\n');

    // Get all devices and export to file
    await fetcher.exportAllDeviceData();

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

if (require.main === module) {
  main();
}

module.exports = AllDevicesFetcher;
