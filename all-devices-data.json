{"deviceInfos": [{"name": "i0482 منزل ماجد الطويل", "deviceSerial": "Q27993962", "fullSerial": "20230904CCWRQ27993962WU", "deviceType": "DS-PWA64-L-WB", "devicePicPrefix": "https://devpic.ezvizlife.com/device/image/DVR/", "version": "V1.3.0 build 241205", "supportExt": "{\"44\":\"3\",\"49\":\"1\",\"193\":\"1\",\"591\":\"3\",\"152\":\"1\",\"451\":\"4\",\"232\":\"0\",\"452\":\"1\",\"650\":\"1\",\"178\":\"1\",\"233\":\"0\",\"255\":\"1\",\"234\":\"4\",\"235\":\"1\",\"479\":\"1\",\"535\":\"1\",\"579\":\"1\",\"516\":\"1\",\"538\":\"1\",\"30\":\"0\",\"31\":\"0\",\"10\":\"1\",\"562\":\"1\",\"288\":\"1\",\"587\":\"1\",\"528\":\"1\"}", "status": 1, "userDeviceCreateTime": "2024-08-14 12:46:26", "casIp": "sgpcas.ezvizlife.com", "casPort": 6500, "channelNumber": 16, "hik": true, "deviceCategory": "HIK_ALARM_PRODUCTS", "deviceSubCategory": "SCPPWA", "ezDeviceCapability": "{\"262\":\"1\",\"175\":\"1\",\"263\":\"0\",\"264\":\"1\",\"232\":\"0\",\"265\":\"1\",\"551\":\"1\",\"233\":\"0\",\"266\":\"0\",\"234\":1,\"267\":\"1\",\"289\":\"1\",\"30\":\"0\",\"31\":\"0\"}", "customType": "DS-PWA64-L-WB", "offlineTime": "2025-06-26 23:58:29", "offlineNotify": 0, "accessPlatform": true, "deviceDomain": "Q27993962", "instructionBook": "http://devpic.ezvizlife.com/device/image/DS-PWA64-L-WB/instruction.jpeg", "deviceShareInfo": null, "feature": null, "riskLevel": 0, "offlineTimestamp": 1750982309099, "extPermission": null, "tags": null, "isOwner": 1}, {"name": "0059 مكتب قصي شعبان", "deviceSerial": "Q99157287", "fullSerial": "20231205CCWRQ99157287WU", "deviceType": "DS-PWA64-L-WB", "devicePicPrefix": "https://devpic.ezvizlife.com/device/image/DVR/", "version": "V1.3.0 build 241205", "supportExt": "{\"44\":\"3\",\"49\":\"1\",\"193\":\"1\",\"591\":\"3\",\"152\":\"1\",\"451\":\"4\",\"232\":\"0\",\"452\":\"1\",\"650\":\"1\",\"178\":\"1\",\"233\":\"0\",\"255\":\"1\",\"234\":\"4\",\"235\":\"1\",\"479\":\"1\",\"535\":\"1\",\"579\":\"1\",\"516\":\"1\",\"538\":\"1\",\"30\":\"0\",\"31\":\"0\",\"10\":\"1\",\"562\":\"1\",\"288\":\"1\",\"587\":\"1\",\"528\":\"1\"}", "status": 1, "userDeviceCreateTime": "2024-10-19 07:47:26", "casIp": "sgpcas.ezvizlife.com", "casPort": 6500, "channelNumber": 16, "hik": true, "deviceCategory": "HIK_ALARM_PRODUCTS", "deviceSubCategory": "SCPPWA", "ezDeviceCapability": "{\"262\":\"1\",\"175\":\"1\",\"263\":\"0\",\"264\":\"1\",\"232\":\"0\",\"265\":\"1\",\"551\":\"1\",\"233\":\"0\",\"266\":\"0\",\"234\":1,\"267\":\"1\",\"289\":\"1\",\"30\":\"0\",\"31\":\"0\"}", "customType": "DS-PWA64-L-WB", "offlineTime": "2025-06-29 16:27:49", "offlineNotify": 0, "accessPlatform": true, "deviceDomain": "Q99157287", "instructionBook": "http://devpic.ezvizlife.com/device/image/DS-PWA64-L-WB/instruction.jpeg", "deviceShareInfo": {"isShared": 1, "shareType": 1, "permission": 1, "inviterName": "81btbr", "extPermission": null}, "feature": null, "riskLevel": 0, "offlineTimestamp": 1751214469328, "extPermission": null, "tags": null, "isOwner": 1}, {"name": "0084 بن العربي دوار القب", "deviceSerial": "Q99600784", "fullSerial": "20240921CCWRQ99600784WU", "deviceType": "DS-PWA64-L-WB", "devicePicPrefix": "https://devpic.ezvizlife.com/device/image/DVR/", "version": "V1.3.0 build 250407", "supportExt": "{\"44\":\"3\",\"49\":\"1\",\"193\":\"1\",\"591\":\"3\",\"152\":\"1\",\"451\":\"4\",\"232\":\"0\",\"452\":\"1\",\"650\":\"1\",\"178\":\"1\",\"233\":\"0\",\"255\":\"1\",\"234\":\"4\",\"235\":\"1\",\"479\":\"1\",\"535\":\"1\",\"579\":\"1\",\"516\":\"1\",\"538\":\"1\",\"30\":\"0\",\"31\":\"0\",\"10\":\"1\",\"562\":\"1\",\"288\":\"1\",\"587\":\"1\",\"528\":\"1\"}", "status": 1, "userDeviceCreateTime": "2025-04-09 18:15:32", "casIp": "sgpcas.ezvizlife.com", "casPort": 6500, "channelNumber": 16, "hik": true, "deviceCategory": "HIK_ALARM_PRODUCTS", "deviceSubCategory": "SCPPWA", "ezDeviceCapability": "{\"262\":\"1\",\"175\":\"1\",\"263\":\"0\",\"264\":\"1\",\"232\":\"0\",\"265\":\"1\",\"551\":\"1\",\"233\":\"0\",\"266\":\"0\",\"234\":1,\"267\":\"1\",\"289\":\"1\",\"30\":\"0\",\"31\":\"0\"}", "customType": "DS-PWA64-L-WB", "offlineTime": "2025-06-21 11:27:40", "offlineNotify": 0, "accessPlatform": true, "deviceDomain": "Q99600784", "instructionBook": "http://devpic.ezvizlife.com/device/image/DS-PWA64-L-WB/instruction.jpeg", "deviceShareInfo": null, "feature": null, "riskLevel": 0, "offlineTimestamp": 1750505260943, "extPermission": null, "tags": null, "isOwner": 1}, {"name": "502 مجوهرات ابوعين", "deviceSerial": "Q27993886", "fullSerial": "DS-PWA64-L-WB20230904AAWRQ27993886WU", "deviceType": "DS-PWA64-L-WB", "devicePicPrefix": "https://devpic.ezvizlife.com/device/image/DVR/", "version": "V1.2.9 build 240621", "supportExt": "{\"44\":\"3\",\"46\":\"1\",\"26\":\"4\",\"49\":\"1\",\"153\":\"1\",\"175\":\"0\",\"154\":\"0\",\"451\":\"4\",\"232\":\"0\",\"233\":\"0\",\"255\":\"1\",\"234\":16,\"235\":\"1\",\"579\":\"1\",\"516\":\"1\",\"30\":\"0\",\"31\":\"0\",\"10\":\"1\",\"13\":\"0\",\"1\":\"0\",\"2\":\"0\",\"145\":\"1\",\"288\":\"1\",\"3\":\"0\",\"4\":\"0\",\"587\":\"1\",\"5\":\"0\",\"6\":\"0\",\"7\":\"1\",\"106\":\"2\",\"249\":\"1\",\"8\":\"0\",\"9\":\"0\"}", "status": 1, "userDeviceCreateTime": "2024-02-24 10:34:57", "casIp": "sgpcas.ezvizlife.com", "casPort": 6500, "channelNumber": 16, "hik": true, "deviceCategory": "HIK_ALARM_PRODUCTS", "deviceSubCategory": "SCPPWA", "ezDeviceCapability": "{\"262\":\"1\",\"175\":\"1\",\"263\":\"0\",\"264\":\"1\",\"232\":\"0\",\"265\":\"1\",\"551\":\"1\",\"233\":\"0\",\"266\":\"0\",\"234\":1,\"267\":\"1\",\"289\":\"1\",\"30\":\"0\",\"31\":\"0\"}", "customType": "DS-PWA64-L-WB", "offlineTime": "2025-06-02 14:50:14", "offlineNotify": 0, "accessPlatform": true, "deviceDomain": "Q27993886", "instructionBook": "http://devpic.ezvizlife.com/device/image/DS-PWA64-L-WB/instruction.jpeg", "deviceShareInfo": {"isShared": 1, "shareType": 1, "permission": 1, "inviterName": "81btbr", "extPermission": null}, "feature": null, "riskLevel": 0, "offlineTimestamp": 1748875814037, "extPermission": null, "tags": null, "isOwner": 1}, {"name": "من<PERSON><PERSON> أحمد ابو الرب 0504", "deviceSerial": "Q27993766", "fullSerial": "DS-PWA64-L-WB20230902AAWRQ27993766WU", "deviceType": "DS-PWA64-L-WB", "devicePicPrefix": "https://devpic.ezvizlife.com/device/image/DVR/", "version": "V1.2.9 build 240621", "supportExt": "{\"44\":\"3\",\"46\":\"1\",\"26\":\"4\",\"49\":\"1\",\"153\":\"1\",\"175\":\"0\",\"154\":\"0\",\"451\":\"4\",\"232\":\"0\",\"233\":\"0\",\"255\":\"1\",\"234\":16,\"235\":\"1\",\"579\":\"1\",\"516\":\"1\",\"30\":\"0\",\"31\":\"0\",\"10\":\"1\",\"13\":\"0\",\"1\":\"0\",\"2\":\"0\",\"145\":\"1\",\"288\":\"1\",\"3\":\"0\",\"4\":\"0\",\"587\":\"1\",\"5\":\"0\",\"6\":\"0\",\"7\":\"1\",\"106\":\"2\",\"249\":\"1\",\"8\":\"0\",\"9\":\"0\"}", "status": 1, "userDeviceCreateTime": "2024-05-11 14:14:07", "casIp": "sgpcas.ezvizlife.com", "casPort": 6500, "channelNumber": 16, "hik": true, "deviceCategory": "HIK_ALARM_PRODUCTS", "deviceSubCategory": "SCPPWA", "ezDeviceCapability": "{\"262\":\"1\",\"175\":\"1\",\"263\":\"0\",\"264\":\"1\",\"232\":\"0\",\"265\":\"1\",\"551\":\"1\",\"233\":\"0\",\"266\":\"0\",\"234\":1,\"267\":\"1\",\"289\":\"1\",\"30\":\"0\",\"31\":\"0\"}", "customType": "DS-PWA64-L-WB", "offlineTime": "2025-06-30 14:21:37", "offlineNotify": 0, "accessPlatform": true, "deviceDomain": "Q27993766", "instructionBook": "http://devpic.ezvizlife.com/device/image/DS-PWA64-L-WB/instruction.jpeg", "deviceShareInfo": {"isShared": 1, "shareType": 1, "permission": 1, "inviterName": "81btbr", "extPermission": null}, "feature": null, "riskLevel": 0, "offlineTimestamp": 1751293297496, "extPermission": null, "tags": null, "isOwner": 1}, {"name": "0513  مايسترو الزرقاء", "deviceSerial": "Q99157281", "fullSerial": "DS-PWA64-L-WB20231205AAWRQ99157281WU", "deviceType": "DS-PWA64-L-WB", "devicePicPrefix": "https://devpic.ezvizlife.com/device/image/DVR/", "version": "V1.2.9 build 240621", "supportExt": "{\"44\":\"3\",\"46\":\"1\",\"26\":\"4\",\"49\":\"1\",\"153\":\"1\",\"175\":\"0\",\"154\":\"0\",\"451\":\"4\",\"232\":\"0\",\"233\":\"0\",\"255\":\"1\",\"234\":16,\"235\":\"1\",\"579\":\"1\",\"516\":\"1\",\"30\":\"0\",\"31\":\"0\",\"10\":\"1\",\"13\":\"0\",\"1\":\"0\",\"2\":\"0\",\"145\":\"1\",\"288\":\"1\",\"3\":\"0\",\"4\":\"0\",\"587\":\"1\",\"5\":\"0\",\"6\":\"0\",\"7\":\"1\",\"106\":\"2\",\"249\":\"1\",\"8\":\"0\",\"9\":\"0\"}", "status": 1, "userDeviceCreateTime": "2024-09-11 12:15:13", "casIp": "sgpcas.ezvizlife.com", "casPort": 6500, "channelNumber": 16, "hik": true, "deviceCategory": "HIK_ALARM_PRODUCTS", "deviceSubCategory": "SCPPWA", "ezDeviceCapability": "{\"262\":\"1\",\"175\":\"1\",\"263\":\"0\",\"264\":\"1\",\"232\":\"0\",\"265\":\"1\",\"551\":\"1\",\"233\":\"0\",\"266\":\"0\",\"234\":1,\"267\":\"1\",\"289\":\"1\",\"30\":\"0\",\"31\":\"0\"}", "customType": "DS-PWA64-L-WB", "offlineTime": "2025-06-30 20:23:43", "offlineNotify": 0, "accessPlatform": true, "deviceDomain": "Q99157281", "instructionBook": "http://devpic.ezvizlife.com/device/image/DS-PWA64-L-WB/instruction.jpeg", "deviceShareInfo": null, "feature": null, "riskLevel": 0, "offlineTimestamp": 1751315023112, "extPermission": null, "tags": null, "isOwner": 1}, {"name": "0168 منزل سامي ابوعيد", "deviceSerial": "Q99599901", "fullSerial": "20240921CCWRQ99599901WU", "deviceType": "DS-PWA64-L-WB", "devicePicPrefix": "https://devpic.ezvizlife.com/device/image/DVR/", "version": "V1.3.0 build 250411", "supportExt": "{\"44\":\"3\",\"49\":\"1\",\"193\":\"1\",\"591\":\"3\",\"152\":\"1\",\"451\":\"4\",\"232\":\"0\",\"452\":\"1\",\"650\":\"1\",\"178\":\"1\",\"233\":\"0\",\"255\":\"1\",\"234\":\"4\",\"235\":\"1\",\"479\":\"1\",\"535\":\"1\",\"579\":\"1\",\"516\":\"1\",\"538\":\"1\",\"30\":\"0\",\"31\":\"0\",\"10\":\"1\",\"562\":\"1\",\"288\":\"1\",\"587\":\"1\",\"528\":\"1\"}", "status": 1, "userDeviceCreateTime": "2025-04-24 12:45:33", "casIp": "sgpcas.ezvizlife.com", "casPort": 6500, "channelNumber": 16, "hik": true, "deviceCategory": "HIK_ALARM_PRODUCTS", "deviceSubCategory": "SCPPWA", "ezDeviceCapability": "{\"262\":\"1\",\"175\":\"1\",\"263\":\"0\",\"264\":\"1\",\"232\":\"0\",\"265\":\"1\",\"551\":\"1\",\"233\":\"0\",\"266\":\"0\",\"234\":1,\"267\":\"1\",\"289\":\"1\",\"30\":\"0\",\"31\":\"0\"}", "customType": "DS-PWA64-L-WB", "offlineTime": "2025-07-01 17:33:57", "offlineNotify": 0, "accessPlatform": true, "deviceDomain": "Q99599901", "instructionBook": "http://devpic.ezvizlife.com/device/image/DS-PWA64-L-WB/instruction.jpeg", "deviceShareInfo": null, "feature": null, "riskLevel": 0, "offlineTimestamp": 1751391237242, "extPermission": null, "tags": null, "isOwner": 1}, {"name": "0427 منزل عيسى النمري", "deviceSerial": "Q16206023", "fullSerial": "20220915CCWRQ16206023WU", "deviceType": "DS-PWA96-M-WB", "devicePicPrefix": "https://devpic.ezvizlife.com/device/image/DVR/", "version": "V1.3.0 build 241205", "supportExt": "{\"44\":\"3\",\"49\":\"1\",\"193\":\"1\",\"591\":\"3\",\"152\":\"1\",\"451\":\"4\",\"232\":\"0\",\"452\":\"1\",\"650\":\"1\",\"178\":\"1\",\"233\":\"0\",\"255\":\"1\",\"234\":\"4\",\"235\":\"1\",\"479\":\"1\",\"535\":\"1\",\"579\":\"1\",\"516\":\"1\",\"538\":\"1\",\"30\":\"0\",\"31\":\"0\",\"10\":\"1\",\"562\":\"1\",\"288\":\"1\",\"587\":\"1\",\"528\":\"1\"}", "status": 1, "userDeviceCreateTime": "2024-06-11 13:03:54", "casIp": "sgpcas.ezvizlife.com", "casPort": 6500, "channelNumber": 16, "hik": true, "deviceCategory": "HIK_ALARM_PRODUCTS", "deviceSubCategory": "SCPPWA", "ezDeviceCapability": "{\"262\":\"1\",\"175\":\"1\",\"263\":\"0\",\"264\":\"1\",\"232\":\"0\",\"265\":\"1\",\"551\":\"1\",\"233\":\"0\",\"266\":\"0\",\"234\":1,\"267\":\"1\",\"289\":\"1\",\"30\":\"0\",\"31\":\"0\"}", "customType": "DS-PWA96-M-WB", "offlineTime": "2025-07-01 23:10:56", "offlineNotify": 0, "accessPlatform": true, "deviceDomain": "Q16206023", "instructionBook": "http://devpic.ezvizlife.com/device/image/DS-PWA96-M-WB/instruction.jpeg", "deviceShareInfo": null, "feature": null, "riskLevel": 0, "offlineTimestamp": 1751411456011, "extPermission": null, "tags": null, "isOwner": 1}], "statusInfos": {"Q99157287": {"diskNum": 0, "diskState": "----------------", "globalStatus": 0, "pirStatus": 0, "isEncrypt": 0, "upgradeAvailable": 1, "upgradeProcess": 100, "upgradeStatus": 1, "alarmSoundMode": -1, "cloudType": 1, "diskStatus": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "privacyStatus": 0, "optionals": {"latestUnbandTime": "1701739340753", "updateCode": "0", "device_screen_overtime": "-1", "language": "ENGLISH", "device_auto_rouse": "-1", "device_bell": "{\"Type\":0,\"CustomRingIndex\":0,\"Volume\":0}", "Record_Mode": "{  \"mode\": 31161600 }", "DB_Chime_ConnInfo": "{  \"Conn\": \"0\",  \"Name\": \"\",  \"Address\": \"\",  \"Mask\": \"\",  \"Gateway\": \"\",  \"Signal\": \"0\",  \"SSID\": \"\",  \"Dbserial\": \"\" }", "device_wifi_mode": "-1", "wanIp": "***************", "device_ICR_DSS": "{  \"mode\": 255,  \"sensitivity\": 255,  \"autoTimeBegin\": \"-1:65535\",  \"autoTimeEnd\": \"-1:65535\" }", "device_static_DNS": "-1", "CustomVoice": "[]", "Route_LinkageStatus": "0", "daylightSavingTime": "0", "Battery_WorkStatus": "{\"KeepAlive\":0,\"WorkTime\":0,\"SpecialBusiness\":0}", "Alarm_DetectHumanCar": "{\"type\":0}", "tzCode": "140", "updateProcessExtend": "", "OSD": "[{\"name\":\"0059 مكتب قصي شعبان\",\"channel\":\"0\"}]", "CustomVoice_Volume": "{\"volume\":-1,\"microphone_volume\":-1}", "diskHealth": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "OnlineStatus": "1", "timeZone": "UTC", "device_video_time": "-1", "Alarm_Light": "{\"luminance\":-1}", "diskCapacity": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "IndicatorLight": "{  \"enable\": 0,  \"mode\": 0 }", "timeFormat": "0", "ICR": "0", "superState": "0", "voiceIndex": "0", "batteryCameraWorkMode": "0", "CertificationStd": "{\"version\":\"ENVer\"}", "latestUnbindTime": "1701739340753", "device_screen_lightness": "-1", "lastUpgradeTime": "1740394194160"}}, "Q99600784": {"diskNum": 0, "globalStatus": 0, "pirStatus": 0, "isEncrypt": 0, "upgradeAvailable": 0, "upgradeProcess": 0, "upgradeStatus": 2, "alarmSoundMode": 0, "cloudType": 1, "privacyStatus": 0, "optionals": {"latestUnbandTime": "1744033275265", "updateCode": "0", "device_screen_overtime": "-1", "OnlineStatus": "1", "timeZone": "UTC", "device_video_time": "-1", "device_auto_rouse": "-1", "wanIp": "**************", "device_ICR_DSS": "{  \"mode\": 255,  \"sensitivity\": 255,  \"autoTimeBegin\": \"-1:65535\",  \"autoTimeEnd\": \"-1:65535\" }", "daylightSavingTime": "0", "timeFormat": "0", "tzCode": "140", "superState": "0", "CertificationStd": "{\"version\":\"ENVer\"}", "latestUnbindTime": "1744033275265", "updateProcessExtend": "", "device_screen_lightness": "-1"}}, "Q27993962": {"diskNum": 0, "diskState": "----------------", "globalStatus": 1, "pirStatus": 0, "isEncrypt": 0, "upgradeAvailable": 1, "upgradeProcess": 0, "upgradeStatus": 2, "alarmSoundMode": -1, "cloudType": 1, "diskStatus": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "privacyStatus": 0, "optionals": {"latestUnbandTime": "1723543508664", "updateCode": "0", "device_screen_overtime": "-1", "language": "ENGLISH", "device_auto_rouse": "-1", "device_bell": "{\"Type\":0,\"CustomRingIndex\":0,\"Volume\":0}", "Record_Mode": "{  \"mode\": 31161600 }", "DB_Chime_ConnInfo": "{  \"Conn\": \"0\",  \"Name\": \"\",  \"Address\": \"\",  \"Mask\": \"\",  \"Gateway\": \"\",  \"Signal\": \"0\",  \"SSID\": \"\",  \"Dbserial\": \"\" }", "device_wifi_mode": "-1", "wanIp": "************", "device_ICR_DSS": "{  \"mode\": 255,  \"sensitivity\": 255,  \"autoTimeBegin\": \"-1:65535\",  \"autoTimeEnd\": \"-1:65535\" }", "device_static_DNS": "-1", "CustomVoice": "[]", "Route_LinkageStatus": "0", "hfullViewURL": "", "daylightSavingTime": "0", "Battery_WorkStatus": "{\"KeepAlive\":0,\"WorkTime\":0,\"SpecialBusiness\":0}", "Alarm_DetectHumanCar": "{\"type\":0}", "tzCode": "140", "updateProcessExtend": "", "OSD": "[{\"name\":\"iSecure 0482 منزل ماجد الطويل\",\"channel\":\"0\"}]", "CustomVoice_Volume": "{\"volume\":0,\"microphone_volume\":0}", "diskHealth": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "OnlineStatus": "1", "device_video_time": "-1", "timeZone": "UTC", "Alarm_Light": "{\"luminance\":-1}", "diskCapacity": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "IndicatorLight": "{  \"enable\": 0,  \"mode\": 0 }", "timeFormat": "0", "ICR": "0", "superState": "0", "voiceIndex": "0", "batteryCameraWorkMode": "0", "CertificationStd": "{\"version\":\"ENVer\"}", "latestUnbindTime": "1723543508664", "lastUpgradeTime": "1723639807032", "device_screen_lightness": "-1"}}, "Q27993886": {"diskNum": 0, "diskState": "----------------", "globalStatus": 1, "pirStatus": 0, "isEncrypt": 0, "upgradeAvailable": 1, "upgradeProcess": 0, "upgradeStatus": 2, "alarmSoundMode": -1, "cloudType": 1, "diskStatus": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "privacyStatus": 0, "optionals": {"latestUnbandTime": "1708518908047", "updateCode": "0", "device_screen_overtime": "-1", "language": "ENGLISH", "device_auto_rouse": "-1", "device_bell": "{\"Type\":0,\"CustomRingIndex\":0,\"Volume\":0}", "DB_Chime_ConnInfo": "{  \"Conn\": \"0\",  \"Name\": \"\",  \"Address\": \"\",  \"Mask\": \"\",  \"Gateway\": \"\",  \"Signal\": \"0\",  \"SSID\": \"\",  \"Dbserial\": \"\" }", "Record_Mode": "{  \"mode\": 31161600 }", "device_wifi_mode": "-1", "wanIp": "************", "device_ICR_DSS": "{  \"mode\": 255,  \"sensitivity\": 255,  \"autoTimeBegin\": \"-1:65535\",  \"autoTimeEnd\": \"-1:65535\" }", "device_static_DNS": "-1", "CustomVoice": "[]", "Route_LinkageStatus": "0", "daylightSavingTime": "1", "Battery_WorkStatus": "{\"KeepAlive\":0,\"WorkTime\":0,\"SpecialBusiness\":0}", "Alarm_DetectHumanCar": "{\"type\":0}", "tzCode": "110", "updateProcessExtend": "", "OSD": "[{\"name\":\"502 مجوهرات ابوعين\",\"channel\":\"0\"}]", "CustomVoice_Volume": "{\"volume\":-1,\"microphone_volume\":-1}", "diskHealth": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "OnlineStatus": "1", "timeZone": "UTC+02:00", "device_video_time": "-1", "Alarm_Light": "{\"luminance\":-1}", "diskCapacity": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "IndicatorLight": "{  \"enable\": 0,  \"mode\": 0 }", "timeFormat": "0", "ICR": "0", "superState": "0", "voiceIndex": "0", "batteryCameraWorkMode": "0", "CertificationStd": "{\"version\":\"ENVer\"}", "latestUnbindTime": "1708518908047", "lastUpgradeTime": "1721920983675", "device_screen_lightness": "-1"}}, "Q27993766": {"diskNum": 0, "globalStatus": 0, "pirStatus": 0, "isEncrypt": 0, "upgradeAvailable": 1, "upgradeProcess": 0, "upgradeStatus": 2, "alarmSoundMode": -1, "cloudType": 1, "privacyStatus": 0, "optionals": {"latestUnbandTime": "1715436625247", "updateCode": "0", "device_screen_overtime": "-1", "language": "ENGLISH", "device_auto_rouse": "-1", "DB_Chime_ConnInfo": "{  \"Conn\": \"0\",  \"Name\": \"\",  \"Address\": \"\",  \"Mask\": \"\",  \"Gateway\": \"\",  \"Signal\": \"0\",  \"SSID\": \"\",  \"Dbserial\": \"\" }", "Record_Mode": "{  \"mode\": 31161600 }", "wanIp": "*************", "device_ICR_DSS": "{  \"mode\": 255,  \"sensitivity\": 255,  \"autoTimeBegin\": \"-1:65535\",  \"autoTimeEnd\": \"-1:65535\" }", "CustomVoice": "[]", "Route_LinkageStatus": "0", "hfullViewURL": "", "daylightSavingTime": "1", "Alarm_DetectHumanCar": "{\"type\":0}", "tzCode": "110", "updateProcessExtend": "", "OSD": "[{\"name\":\"منز<PERSON> أحمد ابو الرب 0504\",\"channel\":\"0\"}]", "CustomVoice_Volume": "{\"volume\":-1,\"microphone_volume\":-1}", "OnlineStatus": "1", "timeZone": "UTC+02:00", "device_video_time": "-1", "Alarm_Light": "{\"luminance\":-1}", "timeFormat": "0", "ICR": "0", "superState": "0", "voiceIndex": "0", "batteryCameraWorkMode": "0", "CertificationStd": "{\"version\":\"ENVer\"}", "latestUnbindTime": "1715436625247", "device_screen_lightness": "-1"}}, "Q99157281": {"diskNum": 0, "diskState": "----------------", "globalStatus": 1, "pirStatus": 0, "isEncrypt": 0, "upgradeAvailable": 1, "upgradeProcess": 0, "upgradeStatus": 2, "alarmSoundMode": -1, "cloudType": 1, "diskStatus": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "privacyStatus": 0, "optionals": {"latestUnbandTime": "1726056870073", "updateCode": "0", "device_screen_overtime": "-1", "language": "ENGLISH", "device_auto_rouse": "-1", "device_bell": "{\"Type\":0,\"CustomRingIndex\":0,\"Volume\":0}", "DB_Chime_ConnInfo": "{  \"Conn\": \"0\",  \"Name\": \"\",  \"Address\": \"\",  \"Mask\": \"\",  \"Gateway\": \"\",  \"Signal\": \"0\",  \"SSID\": \"\",  \"Dbserial\": \"\" }", "Record_Mode": "{  \"mode\": 31161600 }", "device_wifi_mode": "-1", "wanIp": "*************", "device_ICR_DSS": "{  \"mode\": 255,  \"sensitivity\": 255,  \"autoTimeBegin\": \"-1:65535\",  \"autoTimeEnd\": \"-1:65535\" }", "device_static_DNS": "-1", "CustomVoice": "[]", "Route_LinkageStatus": "0", "hfullViewURL": "", "daylightSavingTime": "0", "Battery_WorkStatus": "{\"KeepAlive\":0,\"WorkTime\":0,\"SpecialBusiness\":0}", "Alarm_DetectHumanCar": "{\"type\":0}", "tzCode": "140", "updateProcessExtend": "", "OSD": "[{\"name\":\"0513  مايسترو الزرقاء\",\"channel\":\"0\"}]", "CustomVoice_Volume": "{\"volume\":-1,\"microphone_volume\":-1}", "diskHealth": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "OnlineStatus": "1", "timeZone": "UTC", "device_video_time": "-1", "Alarm_Light": "{\"luminance\":-1}", "diskCapacity": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "IndicatorLight": "{  \"enable\": 0,  \"mode\": 0 }", "timeFormat": "0", "ICR": "0", "superState": "0", "voiceIndex": "0", "batteryCameraWorkMode": "0", "CertificationStd": "{\"version\":\"ENVer\"}", "latestUnbindTime": "1726056870073", "lastUpgradeTime": "1743675222117", "device_screen_lightness": "-1"}}, "Q99599901": {"diskNum": 0, "diskState": "----------------", "globalStatus": 0, "pirStatus": 0, "isEncrypt": 0, "upgradeAvailable": 1, "upgradeProcess": 0, "upgradeStatus": 2, "alarmSoundMode": -1, "cloudType": 1, "diskStatus": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "privacyStatus": 0, "optionals": {"latestUnbandTime": "1744640822344", "updateCode": "0", "device_screen_overtime": "-1", "language": "ENGLISH", "device_auto_rouse": "-1", "device_bell": "{\"Type\":0,\"CustomRingIndex\":0,\"Volume\":0}", "Record_Mode": "{  \"mode\": 31161600 }", "DB_Chime_ConnInfo": "{  \"Conn\": \"0\",  \"Name\": \"\",  \"Address\": \"\",  \"Mask\": \"\",  \"Gateway\": \"\",  \"Signal\": \"0\",  \"SSID\": \"\",  \"Dbserial\": \"\" }", "device_wifi_mode": "-1", "wanIp": "**************", "device_ICR_DSS": "{  \"mode\": 255,  \"sensitivity\": 0,  \"autoTimeBegin\": \"00:00\",  \"autoTimeEnd\": \"00:00\" }", "device_static_DNS": "-1", "CustomVoice": "[]", "Route_LinkageStatus": "0", "hfullViewURL": "", "daylightSavingTime": "0", "Battery_WorkStatus": "{\"KeepAlive\":0,\"WorkTime\":0,\"SpecialBusiness\":0}", "Alarm_DetectHumanCar": "{\"type\":0}", "tzCode": "96", "updateProcessExtend": "", "OSD": "[{\"name\":\"AX PRO\",\"channel\":\"0\"}]", "CustomVoice_Volume": "{\"volume\":0,\"microphone_volume\":0}", "diskHealth": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "OnlineStatus": "1", "timeZone": "UTC+03:00", "device_video_time": "-1", "Alarm_Light": "{\"luminance\":-1}", "diskCapacity": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "IndicatorLight": "{  \"enable\": 0,  \"mode\": 0 }", "timeFormat": "0", "ICR": "0", "superState": "0", "voiceIndex": "0", "batteryCameraWorkMode": "0", "CertificationStd": "{\"version\":\"ENVer\"}", "latestUnbindTime": "1744640822344", "lastUpgradeTime": "1739004653331", "device_screen_lightness": "-1"}}, "Q16206023": {"diskNum": 0, "diskState": "----------------", "globalStatus": 0, "pirStatus": 0, "isEncrypt": 0, "upgradeAvailable": 1, "upgradeProcess": 100, "upgradeStatus": 1, "alarmSoundMode": -1, "cloudType": 1, "diskStatus": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "privacyStatus": 0, "optionals": {"updateCode": "0", "device_screen_overtime": "-1", "language": "ENGLISH", "device_auto_rouse": "-1", "device_bell": "{\"Type\":0,\"CustomRingIndex\":0,\"Volume\":0}", "DB_Chime_ConnInfo": "{  \"Conn\": \"0\",  \"Name\": \"\",  \"Address\": \"\",  \"Mask\": \"\",  \"Gateway\": \"\",  \"Signal\": \"0\",  \"SSID\": \"\",  \"Dbserial\": \"\" }", "Record_Mode": "{  \"mode\": 31161600 }", "device_wifi_mode": "0", "wanIp": "*************", "device_ICR_DSS": "{  \"mode\": 255,  \"sensitivity\": 255,  \"autoTimeBegin\": \"-1:65535\",  \"autoTimeEnd\": \"-1:65535\" }", "device_static_DNS": "0", "CustomVoice": "[]", "Route_LinkageStatus": "0", "Battery_WorkStatus": "{\"KeepAlive\":0,\"WorkTime\":0,\"SpecialBusiness\":0}", "daylightSavingTime": "0", "Alarm_DetectHumanCar": "{\"type\":0}", "tzCode": "96", "updateProcessExtend": "", "OSD": "[{\"name\":\"0427 منزل عيسى النمري\",\"channel\":\"0\"}]", "CustomVoice_Volume": "{\"volume\":-1,\"microphone_volume\":-1}", "diskHealth": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "OnlineStatus": "1", "timeZone": "UTC+03:00", "device_video_time": "-1", "Alarm_Light": "{\"luminance\":-1}", "diskCapacity": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "IndicatorLight": "{  \"enable\": 0,  \"mode\": 0 }", "timeFormat": "0", "ICR": "0", "superState": "0", "batteryCameraWorkMode": "0", "voiceIndex": "0", "CertificationStd": "{\"version\":\"ENVer\"}", "lastUpgradeTime": "1734936478367", "device_screen_lightness": "-1"}}}, "switchStatusInfos": {"Q99600784": [{"deviceSerial": "Q99600784", "channelNo": 0, "type": 29, "enable": false}], "Q99157287": [{"deviceSerial": "Q99157287", "channelNo": 0, "type": 15, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 29, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 39, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 41, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 200, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 202, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 300, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 301, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 302, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 303, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 304, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 305, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 306, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 451, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 602, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 603, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 613, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 614, "enable": false}, {"deviceSerial": "Q99157287", "channelNo": 0, "type": 617, "enable": false}], "Q27993962": [{"deviceSerial": "Q27993962", "channelNo": 0, "type": 15, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 29, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 39, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 41, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 200, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 202, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 300, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 301, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 302, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 303, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 304, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 305, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 306, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 451, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 602, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 603, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 613, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 614, "enable": false}, {"deviceSerial": "Q27993962", "channelNo": 0, "type": 617, "enable": false}], "Q27993886": [{"deviceSerial": "Q27993886", "channelNo": 0, "type": 15, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 29, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 39, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 41, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 200, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 202, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 300, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 301, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 302, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 303, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 304, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 305, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 306, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 451, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 602, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 603, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 613, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 614, "enable": false}, {"deviceSerial": "Q27993886", "channelNo": 0, "type": 617, "enable": false}], "Q27993766": [{"deviceSerial": "Q27993766", "channelNo": 0, "type": 15, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 29, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 39, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 41, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 200, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 202, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 300, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 301, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 302, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 303, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 304, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 305, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 306, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 451, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 602, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 603, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 613, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 614, "enable": false}, {"deviceSerial": "Q27993766", "channelNo": 0, "type": 617, "enable": false}], "Q99157281": [{"deviceSerial": "Q99157281", "channelNo": 0, "type": 15, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 29, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 39, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 41, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 200, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 202, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 300, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 301, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 302, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 303, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 304, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 305, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 306, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 451, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 602, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 603, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 613, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 614, "enable": false}, {"deviceSerial": "Q99157281", "channelNo": 0, "type": 617, "enable": false}], "Q99599901": [{"deviceSerial": "Q99599901", "channelNo": 0, "type": 15, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 29, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 39, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 41, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 200, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 202, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 300, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 301, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 302, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 303, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 304, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 305, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 306, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 451, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 602, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 603, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 613, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 614, "enable": false}, {"deviceSerial": "Q99599901", "channelNo": 0, "type": 617, "enable": false}], "Q16206023": [{"deviceSerial": "Q16206023", "channelNo": 0, "type": 15, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 29, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 39, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 41, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 200, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 202, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 300, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 301, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 302, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 303, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 304, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 305, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 306, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 451, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 602, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 603, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 613, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 614, "enable": false}, {"deviceSerial": "Q16206023", "channelNo": 0, "type": 617, "enable": false}]}}