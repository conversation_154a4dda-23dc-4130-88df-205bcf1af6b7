#!/usr/bin/env node

const HikConnectAPI = require('./src/api');
const HikConnectClient = require('./src/client');
const axios = require('axios');

class AlarmAPIExplorer {
  constructor(config) {
    this.config = config;
    const hikConnectAPI = new HikConnectAPI({ baseUrl: config.api_url || 'https://api.hik-connect.com' });
    this.hikConnectClient = new HikConnectClient({ 
      hikConnectAPI, 
      ignoredLocks: [], 
      log: { debug: console.log, error: console.error } 
    });
  }

  async login() {
    const { account, password } = this.config;
    if (!account || !password) {
      throw new Error('Account and password required in config');
    }
    await this.hikConnectClient.login({ account, password });
  }

  // Test various API endpoints for alarm control based on actual device data
  async exploreAlarmEndpoints(deviceSerial, deviceData) {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log(`🔍 Exploring alarm API endpoints for device: ${deviceSerial}\n`);

    // Look at the switchStatusInfos to understand available controls
    const switchInfo = deviceData.switchStatusInfos[deviceSerial];
    if (switchInfo && switchInfo.length > 0) {
      console.log(`🔧 Found ${switchInfo.length} switch controls:`);
      switchInfo.forEach((sw, index) => {
        console.log(`   Switch ${index}:`, JSON.stringify(sw, null, 2));
      });
    }

    // Test endpoints based on the actual API pattern we know works (like unlock)
    const endpoints = [
      // Based on unlock pattern: /v3/devconfig/v1/call/{serial}/{channel}/remote/{action}
      `/v3/devconfig/v1/call/${deviceSerial}/0/remote/arm`,
      `/v3/devconfig/v1/call/${deviceSerial}/0/remote/disarm`,
      `/v3/devconfig/v1/call/${deviceSerial}/1/remote/arm`,
      `/v3/devconfig/v1/call/${deviceSerial}/1/remote/disarm`,

      // Try different channel numbers
      `/v3/devconfig/v1/call/${deviceSerial}/0/remote/switch?switchId=0&status=1`,
      `/v3/devconfig/v1/call/${deviceSerial}/0/remote/switch?switchId=1&status=1`,

      // Configuration endpoints
      `/v3/devconfig/v1/devices/${deviceSerial}`,
      `/v3/devconfig/v1/call/${deviceSerial}/0/config`,
      `/v3/devconfig/v1/call/${deviceSerial}/1/config`,
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`📡 Testing PUT: ${endpoint}`);

        const response = await axios({
          method: 'put',
          url: `${baseUrl}${endpoint}`,
          headers,
          timeout: 5000
        });

        console.log(`✅ PUT ${endpoint} - Status: ${response.status}`);
        if (response.data) {
          console.log(`   Response:`, JSON.stringify(response.data, null, 2));
        }

      } catch (error) {
        if (error.response) {
          console.log(`❌ PUT ${endpoint} - Status: ${error.response.status} (${error.response.statusText})`);
          if (error.response.data && error.response.data.meta) {
            console.log(`   Message: ${error.response.data.meta.message}`);
          }
        } else {
          console.log(`❌ PUT ${endpoint} - Error: ${error.message}`);
        }
      }
    }
  }

  // Test device configuration retrieval and modification
  async exploreDeviceConfig(deviceSerial) {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US', 
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log(`\n🔧 Exploring device configuration for: ${deviceSerial}\n`);

    // Try to get device configuration
    const configEndpoints = [
      `/v3/devconfig/v1/devices/${deviceSerial}`,
      `/v3/devconfig/v1/devices/${deviceSerial}/config`,
      `/v3/devconfig/v1/devices/${deviceSerial}/parameters`,
      `/v3/devconfig/v1/devices/${deviceSerial}/properties`,
    ];

    for (const endpoint of configEndpoints) {
      try {
        const response = await axios({
          method: 'get',
          url: `${baseUrl}${endpoint}`,
          headers,
          timeout: 5000
        });
        
        console.log(`✅ Config GET ${endpoint} - Status: ${response.status}`);
        console.log(`   Response:`, JSON.stringify(response.data, null, 2));
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ Config GET ${endpoint} - Status: ${error.response.status}`);
        } else {
          console.log(`❌ Config GET ${endpoint} - Error: ${error.message}`);
        }
      }
    }
  }

  // Test alarm control commands
  async testAlarmControls(deviceSerial) {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US', 
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log(`\n🚨 Testing alarm control commands for: ${deviceSerial}\n`);

    // Test alarm control commands (using PUT method like unlock)
    const alarmCommands = [
      { name: 'ARM', endpoint: `/v3/devconfig/v1/call/${deviceSerial}/0/remote/arm` },
      { name: 'DISARM', endpoint: `/v3/devconfig/v1/call/${deviceSerial}/0/remote/disarm` },
      { name: 'PARTIAL ARM', endpoint: `/v3/devconfig/v1/call/${deviceSerial}/0/remote/partialarm` },
      { name: 'BYPASS', endpoint: `/v3/devconfig/v1/call/${deviceSerial}/0/remote/bypass` },
    ];

    for (const command of alarmCommands) {
      try {
        console.log(`🔄 Testing ${command.name} command...`);
        
        const response = await axios({
          method: 'put',
          url: `${baseUrl}${command.endpoint}`,
          headers,
          timeout: 5000
        });
        
        console.log(`✅ ${command.name} - Status: ${response.status}`);
        if (response.data) {
          console.log(`   Response:`, JSON.stringify(response.data, null, 2));
        }
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ ${command.name} - Status: ${error.response.status} (${error.response.statusText})`);
          if (error.response.data) {
            console.log(`   Error data:`, JSON.stringify(error.response.data, null, 2));
          }
        } else {
          console.log(`❌ ${command.name} - Error: ${error.message}`);
        }
      }
    }
  }

  // Test switch controls based on device data
  async testSwitchControls(deviceSerial, deviceData) {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log(`\n🔘 Testing switch controls for: ${deviceSerial}\n`);

    const switchInfo = deviceData.switchStatusInfos[deviceSerial];
    if (!switchInfo || switchInfo.length === 0) {
      console.log('❌ No switch controls found for this device');
      return;
    }

    console.log(`📋 Found ${switchInfo.length} switches:`);
    switchInfo.forEach((sw, index) => {
      console.log(`   Switch ${index}:`, sw);
    });

    // Test switch control endpoints
    for (let i = 0; i < switchInfo.length; i++) {
      const switchData = switchInfo[i];

      // Try different switch control patterns
      const switchEndpoints = [
        `/v3/devconfig/v1/call/${deviceSerial}/${switchData.channelNo || 0}/remote/switch?switchId=${i}&status=1`,
        `/v3/devconfig/v1/call/${deviceSerial}/${switchData.channelNo || 0}/remote/switch?switchId=${i}&status=0`,
        `/v3/devconfig/v1/call/${deviceSerial}/0/remote/switch?switchId=${switchData.switchId || i}&status=1`,
        `/v3/devconfig/v1/call/${deviceSerial}/0/remote/switch?switchId=${switchData.switchId || i}&status=0`,
      ];

      for (const endpoint of switchEndpoints) {
        try {
          console.log(`🔄 Testing switch control: ${endpoint}`);

          const response = await axios({
            method: 'put',
            url: `${baseUrl}${endpoint}`,
            headers,
            timeout: 5000
          });

          console.log(`✅ Switch control - Status: ${response.status}`);
          if (response.data) {
            console.log(`   Response:`, JSON.stringify(response.data, null, 2));
          }

        } catch (error) {
          if (error.response) {
            console.log(`❌ Switch control - Status: ${error.response.status} (${error.response.statusText})`);
            if (error.response.data && error.response.data.meta) {
              console.log(`   Message: ${error.response.data.meta.message}`);
            }
          } else {
            console.log(`❌ Switch control - Error: ${error.message}`);
          }
        }
      }
    }
  }

  // Get detailed device information from the main API
  async getDeviceDetails() {
    try {
      const response = await axios({
        method: 'get',
        url: this.hikConnectClient._hikConnectAPI.getDevicesUrl(),
        headers: {
          clientType: 'IECMOBILE',
          lang: 'en-US',
          featureCode: 'hik-connect',
          sessionId: this.hikConnectClient._sessionId
        }
      });

      return response.data;
    } catch (error) {
      console.error('Failed to get device details:', error.message);
      return null;
    }
  }
}

// Main execution
async function main() {
  const config = {
    account: '<EMAIL>',
    password: 'Hatchet1',
    api_url: 'https://api.hik-connect.com'
  };

  const explorer = new AlarmAPIExplorer(config);
  
  try {
    console.log('🔐 Logging into HikConnect...');
    await explorer.login();
    console.log('✅ Login successful!\n');

    // Get device list first
    const deviceData = await explorer.getDeviceDetails();
    if (!deviceData || !deviceData.deviceInfos) {
      console.error('❌ Could not retrieve device information');
      return;
    }

    console.log(`📱 Found ${deviceData.deviceInfos.length} devices\n`);

    // Test with the first device
    const firstDevice = deviceData.deviceInfos[0];
    console.log(`🎯 Testing with device: ${firstDevice.name} (${firstDevice.deviceSerial})\n`);

    // Show device capabilities and support info
    console.log(`📋 Device Info:`);
    console.log(`   Type: ${firstDevice.deviceType}`);
    console.log(`   Category: ${firstDevice.deviceCategory}`);
    console.log(`   Support Extensions: ${firstDevice.supportExt}`);
    console.log(`   EZ Device Capability: ${firstDevice.ezDeviceCapability}\n`);

    // Explore different API endpoints
    await explorer.exploreAlarmEndpoints(firstDevice.deviceSerial, deviceData);
    await explorer.exploreDeviceConfig(firstDevice.deviceSerial);
    await explorer.testAlarmControls(firstDevice.deviceSerial);
    await explorer.testSwitchControls(firstDevice.deviceSerial, deviceData);

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

if (require.main === module) {
  main();
}

module.exports = AlarmAPIExplorer;
