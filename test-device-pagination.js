#!/usr/bin/env node

const HikConnectAPI = require('./src/api');
const HikConnectClient = require('./src/client');
const axios = require('axios');

class DevicePaginationTester {
  constructor(config) {
    this.config = config;
    const hikConnectAPI = new HikConnectAPI({ baseUrl: config.api_url || 'https://api.hik-connect.com' });
    this.hikConnectClient = new HikConnectClient({ 
      hikConnectAPI, 
      ignoredLocks: [], 
      log: { debug: console.log, error: console.error } 
    });
  }

  async login() {
    const { account, password } = this.config;
    if (!account || !password) {
      throw new Error('Account and password required in config');
    }
    await this.hikConnectClient.login({ account, password });
  }

  // Test different pagination parameters
  async testPagination() {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log('🔍 Testing different pagination parameters...\n');

    // Test different pagination combinations
    const paginationTests = [
      { limit: 100, offset: 0, description: 'Default (current)' },
      { limit: 200, offset: 0, description: 'Higher limit' },
      { limit: 50, offset: 0, description: 'Lower limit' },
      { limit: 100, offset: 8, description: 'Next page (offset 8)' },
      { limit: 100, offset: 16, description: 'Third page (offset 16)' },
      { limit: 1000, offset: 0, description: 'Very high limit' },
    ];

    for (const test of paginationTests) {
      try {
        const url = `${baseUrl}/v3/userdevices/v1/devices/pagelist?groupId=-1&limit=${test.limit}&offset=${test.offset}&filter=TIME_PLAN,CONNECTION,SWITCH,STATUS,STATUS_EXT,WIFI,NODISTURB,P2P,KMS,HIDDNS`;
        
        console.log(`📡 Testing ${test.description}: limit=${test.limit}, offset=${test.offset}`);
        
        const response = await axios({
          method: 'get',
          url,
          headers,
          timeout: 10000
        });
        
        const deviceCount = response.data.deviceInfos ? response.data.deviceInfos.length : 0;
        console.log(`✅ Found ${deviceCount} devices`);
        
        if (deviceCount > 0) {
          console.log(`   First device: ${response.data.deviceInfos[0].name} (${response.data.deviceInfos[0].deviceSerial})`);
          if (deviceCount > 1) {
            console.log(`   Last device: ${response.data.deviceInfos[deviceCount-1].name} (${response.data.deviceInfos[deviceCount-1].deviceSerial})`);
          }
        }
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ ${test.description} - Status: ${error.response.status} (${error.response.statusText})`);
        } else {
          console.log(`❌ ${test.description} - Error: ${error.message}`);
        }
      }
      console.log('');
    }
  }

  // Test different group IDs
  async testGroupIds() {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log('🏠 Testing different group IDs...\n');

    // Test different group IDs
    const groupTests = [
      { groupId: -1, description: 'All groups (current)' },
      { groupId: 0, description: 'Group 0' },
      { groupId: 1, description: 'Group 1' },
      { groupId: 2, description: 'Group 2' },
      { groupId: 3, description: 'Group 3' },
    ];

    for (const test of groupTests) {
      try {
        const url = `${baseUrl}/v3/userdevices/v1/devices/pagelist?groupId=${test.groupId}&limit=200&offset=0&filter=TIME_PLAN,CONNECTION,SWITCH,STATUS,STATUS_EXT,WIFI,NODISTURB,P2P,KMS,HIDDNS`;
        
        console.log(`📡 Testing ${test.description}: groupId=${test.groupId}`);
        
        const response = await axios({
          method: 'get',
          url,
          headers,
          timeout: 10000
        });
        
        const deviceCount = response.data.deviceInfos ? response.data.deviceInfos.length : 0;
        console.log(`✅ Found ${deviceCount} devices`);
        
        if (deviceCount > 0) {
          // Show unique device types
          const deviceTypes = [...new Set(response.data.deviceInfos.map(d => d.deviceType))];
          console.log(`   Device types: ${deviceTypes.join(', ')}`);
        }
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ ${test.description} - Status: ${error.response.status} (${error.response.statusText})`);
        } else {
          console.log(`❌ ${test.description} - Error: ${error.message}`);
        }
      }
      console.log('');
    }
  }

  // Test different filters
  async testFilters() {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log('🔧 Testing different filters...\n');

    // Test different filter combinations
    const filterTests = [
      { 
        filter: 'TIME_PLAN,CONNECTION,SWITCH,STATUS,STATUS_EXT,WIFI,NODISTURB,P2P,KMS,HIDDNS', 
        description: 'Current filter' 
      },
      { 
        filter: '', 
        description: 'No filter' 
      },
      { 
        filter: 'STATUS,STATUS_EXT', 
        description: 'Status only' 
      },
      { 
        filter: 'CONNECTION,STATUS', 
        description: 'Connection and status' 
      },
      { 
        filter: 'SWITCH,STATUS,STATUS_EXT', 
        description: 'Switch and status' 
      },
    ];

    for (const test of filterTests) {
      try {
        const filterParam = test.filter ? `&filter=${test.filter}` : '';
        const url = `${baseUrl}/v3/userdevices/v1/devices/pagelist?groupId=-1&limit=200&offset=0${filterParam}`;
        
        console.log(`📡 Testing ${test.description}`);
        
        const response = await axios({
          method: 'get',
          url,
          headers,
          timeout: 10000
        });
        
        const deviceCount = response.data.deviceInfos ? response.data.deviceInfos.length : 0;
        console.log(`✅ Found ${deviceCount} devices`);
        
        if (deviceCount > 0) {
          // Show device categories
          const categories = [...new Set(response.data.deviceInfos.map(d => d.deviceCategory))];
          console.log(`   Categories: ${categories.join(', ')}`);
        }
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ ${test.description} - Status: ${error.response.status} (${error.response.statusText})`);
        } else {
          console.log(`❌ ${test.description} - Error: ${error.message}`);
        }
      }
      console.log('');
    }
  }

  // Test alternative API endpoints
  async testAlternativeEndpoints() {
    const baseUrl = this.hikConnectClient._hikConnectAPI._baseUrl;
    const headers = {
      clientType: 'IECMOBILE',
      lang: 'en-US',
      featureCode: 'hik-connect',
      sessionId: this.hikConnectClient._sessionId
    };

    console.log('🔍 Testing alternative device endpoints...\n');

    // Test different device endpoints
    const endpoints = [
      '/v3/userdevices/v1/devices/list',
      '/v3/userdevices/v1/devices/all',
      '/v3/userdevices/v1/devices',
      '/v3/devices/list',
      '/v3/devices/pagelist',
      '/v3/userdevices/v1/devices/pagelist?limit=1000',
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`📡 Testing: ${endpoint}`);
        
        const response = await axios({
          method: 'get',
          url: `${baseUrl}${endpoint}`,
          headers,
          timeout: 10000
        });
        
        console.log(`✅ ${endpoint} - Status: ${response.status}`);
        if (response.data && response.data.deviceInfos) {
          console.log(`   Found ${response.data.deviceInfos.length} devices`);
        } else if (response.data) {
          console.log(`   Response keys: ${Object.keys(response.data).join(', ')}`);
        }
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ ${endpoint} - Status: ${error.response.status} (${error.response.statusText})`);
        } else {
          console.log(`❌ ${endpoint} - Error: ${error.message}`);
        }
      }
    }
  }
}

// Main execution
async function main() {
  const config = {
    account: '<EMAIL>',
    password: 'Hatchet1',
    api_url: 'https://api.hik-connect.com'
  };

  const tester = new DevicePaginationTester(config);
  
  try {
    console.log('🔐 Logging into HikConnect...');
    await tester.login();
    console.log('✅ Login successful!\n');

    await tester.testPagination();
    await tester.testGroupIds();
    await tester.testFilters();
    await tester.testAlternativeEndpoints();

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

if (require.main === module) {
  main();
}

module.exports = DevicePaginationTester;
